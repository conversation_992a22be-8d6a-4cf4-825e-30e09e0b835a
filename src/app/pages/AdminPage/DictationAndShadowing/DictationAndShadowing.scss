.dictation-shadowing-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .dictation-shadowing-info-card,
  .dictation-shadowing-search-card,
  .dictation-shadowing-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Header styles
  .dictation-shadowing-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dictation-shadowing-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .dictation-shadowing-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create-exercise {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .ant-form-item {
    margin: 0;
  }

  // Status tag styles
  .status-tag-published,
  .status-tag-draft,
  .status-tag-hidden,
  .status-tag-default {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
  }

  .status-tag-published {
    background-color: var(--green-light-1) !important;
    color: var(--green-dark) !important;
    border-color: var(--green-light-1) !important;
  }

  .status-tag-draft {
    background-color: var(--yellow-light-1) !important;
    color: var(--yellow-dark) !important;
    border-color: var(--yellow-light-1) !important;
  }

  .status-tag-hidden {
    background-color: var(--red-light-1) !important;
    color: var(--red-dark) !important;
    border-color: var(--red-light-1) !important;
  }

  .status-tag-default {
    background-color: var(--gray-light-1) !important;
    color: var(--gray-dark) !important;
    border-color: var(--gray-light-1) !important;
  }

  // Table styles
  .dictation-shadowing-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
    }

    .dictation-shadowing-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .exercise-title-value {
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      font-size: 14px;
    }

    .exercise-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      gap: 8px;

      .btn-edit-exercise,
      .btn-delete-exercise {
        &:hover {
          background: var(--background-hover);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .dictation-shadowing-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create-exercise {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}