.email-marketing-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: Segoe UI;

  // Card styles
  .email-marketing-info-card,
  .email-marketing-search-card,
  .email-marketing-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Statistics styles
  .campaign-statistics {
    .stat-cards {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;

      .stat-card {
        display: flex;
        align-items: center;
        padding: 8px;
        border-radius: 8px;
        background-color: #f9f9f9;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-right: 8px;
          color: white;

          i {
            font-size: 14px;
          }
        }

        .stat-content {
          flex: 1;

          .stat-value {
            font-weight: 600;
            font-size: 14px;
            color: var(--typo-colours-primary-black);
            line-height: 1.2;
          }

          .stat-label {
            font-size: 12px;
            color: var(--typo-colours-secondary-grey);
          }
        }

        &.sent .stat-icon {
          background-color: #1890ff;
        }

        &.open .stat-icon {
          background-color: #52c41a;
        }

        &.click .stat-icon {
          background-color: #722ed1;
        }

        &.conversion .stat-icon {
          background-color: #fa8c16;
        }
      }
    }

    .no-stats {
      color: var(--typo-colours-secondary-grey);
      font-style: italic;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 80px;
      background-color: #f9f9f9;
      border-radius: 8px;
    }
  }

  // Header styles
  .email-marketing-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .email-marketing-title {
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      color: var(--typo-colours-primary-black);
    }

    .email-marketing-description {
      font-size: 14px;
      color: var(--typo-colours-secondary-grey);
      margin-bottom: 0;
    }

    .btn-create {
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 8px;
      padding: 10px 24px !important;
    }
  }

  // Search form styles
  .search-form-item {
    margin-bottom: 0;
  }

  .search-buttons-col {
    display: flex;
    justify-content: flex-end;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }

  .form-filter {
    width: 100%;
  }

  .ant-form-item {
    margin: 0;
  }

  .name-value {
    font-weight: 600;
    color: var(--typo-colours-primary-black);
    font-size: 14px;
  }

  // Tag styles
  .ant-tag {
    padding: 2px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
  }

  // Target Groups styles
  .target-groups-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .target-group-tag {
      margin: 0;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      text-align: center;

      &:hover {
        opacity: 0.8;
      }
    }

    .more-groups-tag {
      background-color: #f0f0f0;
      color: #666;
      cursor: pointer;

      &:hover {
        background-color: #e0e0e0;
      }
    }
  }

  .tooltip-groups {
    max-width: 200px;

    .tooltip-group-item {
      padding: 4px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .no-data {
    color: var(--typo-colours-secondary-grey);
    font-style: italic;
  }

  // Table styles
  .email-marketing-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
      border-radius: 8px;
    }

    .ant-table-thead > tr > th {
      background-color: #f8fafc;
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      padding: 16px;
      border-bottom: 2px solid #edf2f7;

      &:first-child {
        padding-left: 24px;
      }

      &:last-child {
        padding-right: 24px;
      }
    }

    .ant-table-tbody > tr > td {
      padding: 16px;
      vertical-align: middle;
      white-space: normal;
      word-break: break-word;
      border-bottom: 1px solid #edf2f7;

      &:first-child {
        padding-left: 24px;
      }

      &:last-child {
        padding-right: 24px;
      }
    }

    .email-marketing-table-row {
      transition: all 0.3s ease;

      &:hover {
        background-color: #f8fafc;
      }

      &:last-child > td {
        border-bottom: none;
      }
    }

    .email-marketing-actions {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 4px;

      .ant-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        padding: 0;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
        }

        &:disabled {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: rgba(0, 0, 0, 0.25);
          cursor: not-allowed;

          &:hover {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: rgba(0, 0, 0, 0.25);
            transform: none;
            box-shadow: none;
          }
        }
      }

      // Edit button - Blue theme (similar to audio button in SpeakingPart1)
      .btn-edit {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;

        &:hover, &:focus {
          background-color: #bae7ff;
          border-color: #1890ff;
          color: #1890ff;
        }
      }

      // Preview button - Purple theme (similar to AI hint button)
      .btn-preview {
        background-color: #f0f2ff;
        border-color: #7b61ff;
        color: #3a18ce;

        &:hover, &:focus {
          background-color: #e6e9ff;
          border-color: #3a18ce;
          color: #3a18ce;
          box-shadow: 0 2px 8px rgba(58, 24, 206, 0.2);
        }
      }

      // Delete button - Red theme (similar to dangerous button)
      .btn-delete {
        background-color: #fff;
        border-color: #ff4d4f;
        color: #ff4d4f;

        &:hover, &:focus {
          background-color: #f8d5d5;
          border-color: #ff4d4f;
          color: #ff4d4f;
        }
      }

      // Toggle status buttons
      .btn-toggle-status {
        &.ant-btn-success {
          background-color: #f6ffed;
          border-color: #52c41a;
          color: #52c41a;

          &:hover, &:focus {
            background-color: #52c41a;
            border-color: #52c41a;
            color: #ffffff;
          }
        }

        &.ant-btn-warning {
          background-color: #fffbe6;
          border-color: #faad14;
          color: #faad14;

          &:hover, &:focus {
            background-color: #faad14;
            border-color: #faad14;
            color: #ffffff;
          }
        }
      }

      // Statistics button - Primary theme
      .btn-statistics {
        background-color: #1890ff;
        border-color: #1890ff;
        color: #fff;

        &:hover, &:focus {
          background-color: #40a9ff;
          border-color: #40a9ff;
          color: #fff;
        }
      }

      // Refresh button - Default theme
      .btn-refresh {
        background-color: #e6f7ff;
        border-color: #91d5ff;
        color: #1890ff;

        &:hover, &:focus {
          background-color: #bae7ff;
          border-color: #1890ff;
          color: #1890ff;
        }
      }

      // Status action buttons với màu sắc riêng cho từng chức năng
      .action-button {
        margin: 0 2px;
      }

      // Send Now button - Bright Orange theme (Gửi ngay)
      .btn-send-now {
        background-color: #fff2e8;
        border-color: #ff7a45;
        color: #ff7a45;

        &:hover, &:focus {
          background-color: #eac7ac;
          border-color: #ff7a45;
          color: #ff7a45;
          box-shadow: 0 2px 8px rgba(255, 122, 69, 0.3);
        }
      }

      // Start/Resume button - Green theme (Bắt đầu/Tiếp tục)
      .btn-start {
        background-color: #f6ffed;
        border-color: #52c41a;
        color: #52c41a;

        &:hover, &:focus {
          background-color: #cae4b1;
          border-color: #52c41a;
          color: #52c41a;
          box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
        }
      }

      // Pause button - Yellow/Orange theme (Tạm dừng)
      .btn-pause {
        background-color: #fffbe6;
        border-color: #faad14;
        color: #faad14;

        &:hover, &:focus {
          background-color: #ecdfa4;
          border-color: #faad14;
          color: #faad14;
          box-shadow: 0 2px 8px rgba(250, 173, 20, 0.3);
        }
      }

      // Complete button - Blue theme (Hoàn thành)
      .btn-complete {
        background-color: #e6f7ff;
        border-color: #1890ff;
        color: #1890ff;

        &:hover, &:focus {
          background-color: #abccdc;
          border-color: #1890ff;
          color: #1890ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
        }
      }

      // Schedule button - Purple theme (Lên lịch)
      .btn-schedule {
        background-color: #f9f0ff;
        border-color: #722ed1;
        color: #722ed1;

        &:hover, &:focus {
          background-color: #c8a6dd;
          border-color: #722ed1;
          color: #722ed1;
          box-shadow: 0 2px 8px rgba(114, 46, 209, 0.3);
        }
      }

      // Reset button - Gray theme (Đặt lại)
      .btn-reset {
        background-color: #f5f5f5;
        border-color: #8c8c8c;
        color: #8c8c8c;

        &:hover, &:focus {
          background-color: #817777;
          border-color: #8c8c8c;
          color: #8c8c8c;
          box-shadow: 0 2px 8px rgba(140, 140, 140, 0.3);
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 768px) {
    .email-marketing-info-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .btn-create {
        width: 100%;
        justify-content: center;
      }
    }

    .form-filter {
      .search-buttons {
        margin-top: 16px;
        width: 100%;
        justify-content: space-between;
      }
    }

    .email-marketing-actions {
      gap: 3px;

      .ant-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }
  }

  @media (max-width: 480px) {
    .email-marketing-actions {
      flex-wrap: wrap;
      gap: 2px;

      .ant-btn {
        width: 26px;
        height: 26px;
        font-size: 11px;
      }
    }
  }
}

/* Styles for EmailCampaignDetail moved to EmailCampaignDetail.scss */
