import React from "react";
import { Card, Empty } from "antd";
import { Line } from "@ant-design/charts";
import { useTranslation } from "react-i18next";

const PerformanceChart = ({ statisticsData }) => {
  const { t } = useTranslation();

  // Check if we have time series data
  const hasData = statisticsData?.timeSeriesData && statisticsData.timeSeriesData.length > 0;

  if (!hasData) {
    return (
      <Card className="email-marketing-table-card">
        <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
        <Empty
          description={t("NO_CHART_DATA")}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  // Transform data for line chart
  const lineData = [];
  statisticsData.timeSeriesData.forEach(item => {
    lineData.push({
      date: item.date,
      value: item.sent || 0,
      category: t("SENT") || 'Đã gửi'
    });
    lineData.push({
      date: item.date,
      value: item.opened || 0,
      category: t("OPENED") || 'Đã mở'
    });
  });

  // Simple line chart configuration
  const config = {
    data: lineData,
    xField: 'date',
    yField: 'value',
    seriesField: 'category',
    smooth: true,
    color: ['#1890ff', '#52c41a'],
    point: {
      size: 3,
    },
    legend: {
      position: 'top',
    },
  };

  return (
    <Card className="email-marketing-table-card">
      <h3>{t("EMAIL_PERFORMANCE_OVER_TIME")}</h3>
      <div style={{ height: 400 }}>
        <Line {...config} />
      </div>
    </Card>
  );
};

export default PerformanceChart;
