import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Form, Input, Select, Slider, InputNumber, Row, Col, message } from "antd";

import { toast } from "@component/ToastProvider";
import Loading from "@component/Loading";
import { AntForm } from "@component/AntForm";
import AntButton from "@component/AntButton";
import { parseJsonToText } from "@common/dataConverter";
import RULE from "@rule";
import { LINK } from "@link";
import { BUTTON } from "@constant";

import { createExplain, getExplainDetail, updateExplain } from "@services/Explain";
import { getGptModel } from "@services/GPTModelPrice";
import "./Explain.scss";

// Custom JSON parser to handle potential issues
const parseTextToJson = (text = "") => {
  if (!text) return null;
  try {
    return JSON.parse(text);
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return null;
  }
};

// Define explain types and task types based on the documentation
const EXPLAIN_TYPES = [
  { value: "idea", label: "Generate Ideas" },
  { value: "find_vocabulary", label: "Find Vocabulary" },
  { value: "help_me_understand", label: "Help Me Understand" },
  { value: "help_me_write", label: "Help Me Write" },
];

const TASK_TYPES = [
  { value: "task1", label: "Task 1" },
  { value: "task2", label: "Task 2" },
  { value: "general", label: "General" },
];

const RESPONSE_FORMATS = [
  { value: "text", label: "Text" },
  { value: "markdown", label: "Markdown" },
  { value: "json_object", label: "JSON Object" },
];

const ExplainDetail = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();

  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [gptModels, setGptModels] = useState([]);

  // Get GPT models on component mount
  useEffect(() => {
    fetchGptModels();
  }, []);

  // Get explain data when id changes
  useEffect(() => {
    if (id ) {
      getExplainData();
    } else {
      // Set default values for new explain
      form.setFieldsValue({
        gptModel: "gpt-4o-mini",
        responseFormat: "text",
        temperature: 0.7,
        maxTokens: 1000,
        taskType: "general",
        explainType: "idea",
      });
      // Make sure loading is false for new explains
      setLoading(false);
    }
  }, [id]);

  const fetchGptModels = async () => {
    try {
      console.log('Fetching GPT models');
      const models = await getGptModel();
      console.log('Received GPT models:', models);

      if (models && Array.isArray(models)) {
        const formattedModels = models.map(model => ({
          value: model.gptModel,
          label: model.gptModel,
        }));
        console.log('Formatted GPT models:', formattedModels);
        setGptModels(formattedModels);
      } else {
        console.warn('No GPT models returned or not an array, using fallback models');
        // Fallback to default models if API returns invalid data
        setGptModels([
          { value: "gpt-4o-mini", label: "GPT-4o Mini" },
          { value: "gpt-4o", label: "GPT-4o" },
          { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
        ]);
      }
    } catch (error) {
      console.error("Error fetching GPT models:", error);
      // Fallback to default models if API fails
      setGptModels([
        { value: "gpt-4o-mini", label: "GPT-4o Mini" },
        { value: "gpt-4o", label: "GPT-4o" },
        { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo" },
      ]);
    }
  };

  const getExplainData = async () => {
    setLoading(true);
    try {
      console.log('Fetching explain data for ID:', id);
      const data = await getExplainDetail(id);
      console.log('Received explain data:', data);

      if (data) {
        // Create a copy of the data to avoid modifying the original
        const formData = { ...data };

        // Convert schemaInstruction from JSON to text for display in the form
        if (formData.schemaInstruction) {
          try {
            formData.schemaInstruction = parseJsonToText(formData.schemaInstruction);
          } catch (error) {
            console.error('Error converting schema to text:', error);
            // If parsing fails, provide a default empty object
            formData.schemaInstruction = '{}';
          }
        }

        // Set default values for fields that might be missing
        if (!formData.gptModel) formData.gptModel = 'gpt-4o-mini';
        if (!formData.responseFormat) formData.responseFormat = 'text';
        if (!formData.temperature) formData.temperature = 0.7;
        if (!formData.maxTokens) formData.maxTokens = 1000;

        console.log('Setting form values:', formData);
        form.setFieldsValue(formData);
      } else {
        console.error('No data returned from getExplainDetail');
        message.error(t("FAILED_TO_LOAD_EXPLAIN_DATA"));
      }
    } catch (error) {
      console.error('Error loading explain data:', error);
      message.error(t("ERROR_LOADING_EXPLAIN_DATA"));
    } finally {
      setLoading(false);
    }
  };
  console.log("loading", isLoading);
  const handleSave = async (values) => {
    try {
      setLoading(true);
      let res;

      // Process the form values
      const formData = { ...values };

      // Convert schemaInstruction from text to JSON
      if (formData.schemaInstruction) {
        const jsonSchema = parseTextToJson(formData.schemaInstruction);
        if (!jsonSchema) {
          message.error(t("INVALID_JSON_SCHEMA"));
          setLoading(false);
          return;
        }
        formData.schemaInstruction = jsonSchema;
      }

      // Ensure numeric values are properly formatted
      if (formData.temperature) {
        formData.temperature = parseFloat(formData.temperature);
      }

      if (formData.maxTokens) {
        formData.maxTokens = parseInt(formData.maxTokens, 10);
      }

      if (id && id !== "create") {
        res = await updateExplain({ _id: id, ...formData });
        if (res) {
          toast.success(t("UPDATE_SUCCESS"));
        } else {
          message.error(t("UPDATE_FAILED"));
        }
      } else {
        res = await createExplain(formData);
        if (res) {
          toast.success(t("CREATE_SUCCESS"));
          navigate(LINK.ADMIN_EXPLAIN);
        } else {
          message.error(t("CREATE_FAILED"));
        }
      }
    } catch (error) {
      console.error('Error saving explain:', error);
      message.error(t("ERROR_SAVING_EXPLAIN"));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN_EXPLAIN);
  };



  return (
    <Loading active={isLoading}>
      <div className="explain-detail-container">
        <div className="explain-detail-header">
          <span className="explain-detail-header__title">
            {id && id !== "create" ? t("EDIT_EXPLAIN") : t("CREATE_EXPLAIN")}
          </span>
        </div>

        <AntForm
          form={form}
          onFinish={handleSave}
          layout="vertical"
          className="explain-form"
          size="large"
        >
          <Row gutter={24}>
            <Col xs={24} lg={24}>
              <AntForm.Item
                name="name"
                label={t("NAME")}
                rules={[RULE.REQUIRED]}
              >
                <Input placeholder={t("ENTER_NAME")} />
              </AntForm.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} lg={12}>
              <AntForm.Item
                name="explainType"
                label={t("EXPLAIN_TYPE")}
                rules={[RULE.REQUIRED]}
              >
                <Select
                  placeholder={t("SELECT_EXPLAIN_TYPE")}
                  options={EXPLAIN_TYPES}
                />
              </AntForm.Item>
            </Col>
            <Col xs={24} lg={12}>
              <AntForm.Item
                name="taskType"
                label={t("TASK_TYPE")}
                rules={[RULE.REQUIRED]}
              >
                <Select
                  placeholder={t("SELECT_TASK_TYPE")}
                  options={TASK_TYPES}
                />
              </AntForm.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} lg={12}>
              <AntForm.Item
                name="gptModel"
                label={t("GPT_MODEL")}
              >
                <Select
                  placeholder={t("SELECT_GPT_MODEL")}
                  options={gptModels.length > 0 ? gptModels : [{ value: "gpt-4o-mini", label: "GPT-4o Mini" }]}
                />
              </AntForm.Item>
            </Col>
            <Col xs={24} lg={12}>
              <AntForm.Item
                name="responseFormat"
                label={t("RESPONSE_FORMAT")}
              >
                <Select
                  placeholder={t("SELECT_RESPONSE_FORMAT")}
                  options={RESPONSE_FORMATS}
                />
              </AntForm.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} lg={12}>
              <AntForm.Item
                name="maxTokens"
                label={t("MAX_TOKENS")}
              >
                <InputNumber min={100} max={4000} style={{ width: '100%' }} />
              </AntForm.Item>
            </Col>
            <Col xs={24} lg={12}>
              <AntForm.Item
                name="temperature"
                label={<>
                  {t("TEMPERATURE")}
                </>}
              >
                <InputNumber
                  min={0}
                  max={2}
                  step={0.1}
                  controls
                  size="large"
                  style={{ width: '100%' }}
                  placeholder="Enter temperature value (0-2)"
                />
              </AntForm.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} lg={24}>
              <AntForm.Item
                name="systemPrompt"
                label="System message"
              >
                <Input.TextArea autoSize={{ minRows: 1, maxRows: 3 }} placeholder="Enter system message" />
              </AntForm.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} lg={24}>
              <AntForm.Item
                name="instruction"
                label={t("INSTRUCTION")}
                rules={[RULE.REQUIRED]}
              >
                <Input.TextArea autoSize={{ minRows: 6, maxRows: 20 }} placeholder={t("ENTER_INSTRUCTION")} />
              </AntForm.Item>
            </Col>
          </Row>


          <Row gutter={24}>
            <Col xs={24} lg={24}>
              <AntForm.Item
                name="schemaInstruction"
                label={t("SCHEMA_INSTRUCTION")}
                rules={[
                  {
                    validator: (_, value) => {
                      if (!value) return Promise.resolve();
                      const jsonSchema = parseTextToJson(value);
                      if (!jsonSchema) {
                        return Promise.reject(new Error(t("PLEASE_ENTER_VALID_JSON")));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <Input.TextArea
                  autoSize={{ minRows: 6, maxRows: 20 }}
                  placeholder={t("ENTER_SCHEMA_INSTRUCTION")}
                  className="schema-json-editor"
                />
              </AntForm.Item>
            </Col>
          </Row>

          <div className="form-actions-submit">
            <AntButton type={BUTTON.WHITE} onClick={handleCancel}>
              {t("CANCEL")}
            </AntButton>
            <AntButton type={BUTTON.DEEP_NAVY} htmlType="submit">
              {t("SAVE")}
            </AntButton>
          </div>
        </AntForm>
      </div>
    </Loading>
  );
};

export default ExplainDetail;
