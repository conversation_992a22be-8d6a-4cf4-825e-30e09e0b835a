.open-ai-cost-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 24px;

  .openai-cost-info-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .openai-cost-info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .openai-cost-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);
        margin-bottom: 8px;
      }

      .openai-cost-description {
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
        margin-bottom: 0;
      }

      .openai-cost-icon {
        font-size: 32px;
        color: var(--primary-colours-blue);
      }
    }
  }

  .openai-cost-info-card,
  .openai-cost-filter-card,
  .openai-cost-tabs-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  .openai-cost-tabs-card {
    padding-bottom: 24px;

    .tabs-header {
      margin-bottom: 16px;

      .tabs-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);
        margin-bottom: 4px;
      }

      .tabs-description {
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
        margin-bottom: 0;
      }
    }

    .openai-cost-tab-content {
      margin-top: 24px;
    }
  }

  // Tabs styling
  .open-ai__tab {
    margin-bottom: 0;

    .ant-tabs-nav {
      margin-bottom: 0;

      &::before {
        display: none;
      }

      .ant-tabs-nav-wrap {
        .ant-tabs-nav-list {
          background-color: var(--background-light-background-1);
          padding: 4px;
          border-radius: 8px;

          .ant-tabs-tab {
            padding: 10px 16px;
            margin: 0 4px;
            border-radius: 6px;
            transition: all 0.3s ease;
            border: none;
            background: transparent;

            .ant-tabs-tab-btn {
              display: flex;
              align-items: center;
              gap: 8px;
              color: var(--typo-colours-secondary-grey);
              font-weight: 500;
              font-size: 14px;
              transition: all 0.3s ease;

              .anticon {
                font-size: 16px;
              }
            }

            &:hover {
              .ant-tabs-tab-btn {
                color: var(--primary-colours-blue);
              }
            }

            &.ant-tabs-tab-active {
              background-color: white;
              box-shadow: var(--shadow-level-1);

              .ant-tabs-tab-btn {
                color: var(--primary-colours-blue);
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }

  .open-ai__chart {
    height: 455px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .open-ai__statistic-card {
    height: 220px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  // Total Cost styles
  .total-cost-container {
    width: 100%;
    font-family: Segoe UI;

    .total-cost-summary-card {
      border-radius: 12px;
      box-shadow: var(--shadow-level-1);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        box-shadow: var(--shadow-level-2);
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .total-cost-title {
      font-size: 20px;
      font-weight: 700;
      color: var(--typo-colours-primary-black);
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background-color: var(--primary-colours-blue);
        border-radius: 4px;
      }
    }

    .total-cost-summary-row {
      margin-bottom: 40px;
    }

    // Stat cards styling
    .total-cost-stat-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: var(--shadow-level-1);
      transition: all 0.3s ease;
      padding: 8px;
      position: relative;
      overflow: hidden;

      &:hover {
        box-shadow: var(--shadow-level-2);
        transform: translateY(-4px);
      }

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
      }

      &.submits-card:before {
        background-color: #1890ff; // Blue
      }

      &.cost-card:before {
        background-color: #f5222d; // Red
      }

      &.average-card:before {
        background-color: #722ed1; // Purple
      }

      .ant-statistic-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        margin-bottom: 12px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);
      }

      .ant-statistic-content {
        font-size: 28px;
        font-weight: 700;
        color: var(--typo-colours-primary-black);
      }

      .stat-description {
        margin-top: 12px;
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
      }

      .anticon {
        font-size: 20px;
      }

      &.submits-card .anticon {
        color: #1890ff;
      }

      &.cost-card .anticon {
        color: #f5222d;
      }

      &.average-card .anticon {
        color: #722ed1;
      }
    }

    // Distribution card styling
    .total-cost-distribution-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: var(--shadow-level-1);
      transition: all 0.3s ease;
      padding: 16px;
      position: relative;
      overflow: hidden;

      &:hover {
        box-shadow: var(--shadow-level-2);
      }

      .distribution-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);

        .distribution-icon {
          color: var(--primary-colours-blue);
          font-size: 20px;
        }
      }

      .distribution-chart {
        height: 100%;
      }
    }

    // Cost breakdown styling
    .total-cost-details {
      .cost-tabs {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: var(--shadow-level-1);
        overflow: hidden;

        .tab-header {
          display: flex;
          border-bottom: 1px solid var(--background-light-background-1);
          padding: 0 16px;

          .tab {
            padding: 16px 24px;
            font-weight: 600;
            color: var(--typo-colours-secondary-grey);
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;

            &.active {
              color: var(--primary-colours-blue);

              &:after {
                content: '';
                position: absolute;
                bottom: -1px;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: var(--primary-colours-blue);
              }
            }
          }
        }

        .tab-content {
          padding: 16px;
        }

        .cost-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .cost-list-item {
          border-radius: 8px;
          transition: all 0.3s ease;
          padding: 0;
          overflow: hidden;

          &:hover {
            box-shadow: var(--shadow-level-2);
            transform: translateY(-2px);
          }

          .ant-card-body {
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 16px;
          }

          .cost-type-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;

            &.media-icon {
              background-color: rgba(24, 144, 255, 0.1);
              color: #1890ff;
            }

            &.text-icon {
              background-color: rgba(82, 196, 26, 0.1);
              color: #52c41a;
            }

            &.image-icon {
              background-color: rgba(250, 173, 20, 0.1);
              color: #faad14;
            }

            &.shadowing-icon {
              background-color: rgba(114, 46, 209, 0.1);
              color: #722ed1;
            }
          }

          .cost-info {
            flex: 1;
            min-width: 0; // Needed for text-overflow to work
          }

          .cost-name-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
          }

          .cost-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--typo-colours-primary-black);
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
          }

          .cost-percent {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-colours-blue);
            background-color: rgba(24, 144, 255, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
          }

          .cost-stats-row {
            display: flex;
            gap: 24px;
            margin-bottom: 12px;
          }

          .cost-stat {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--typo-colours-secondary-grey);

            .stat-icon {
              font-size: 16px;

              &.submits-icon {
                color: #1890ff;
              }

              &.cost-icon {
                color: #f5222d;
              }
            }

            .cost-value {
              color: #f5222d;
              font-weight: 600;
            }
          }

          .cost-progress {
            position: relative;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            background-color: var(--background-light-background-1);

            .progress-bar {
              position: absolute;
              top: 0;
              left: 0;
              height: 100%;
              border-radius: 3px;
              z-index: 1;

              &.media-bar {
                background-color: #1890ff;
              }

              &.text-bar {
                background-color: #52c41a;
              }

              &.image-bar {
                background-color: #faad14;
              }

              &.shadowing-bar {
                background-color: #722ed1;
              }
            }
          }
        }
      }
    }
  }

  .card-title-with-tooltip {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 16px;

    .info-icon {
      color: var(--typo-colours-secondary-grey);
      font-size: 14px;
      cursor: pointer;

      &:hover {
        color: var(--primary-colours-blue);
      }
    }
  }

  .pie-chart-label {
    font-weight: 600;
    font-size: 16px;
    color: var(--primary-colours-blue);
  }

  .cost-value {
    font-weight: 500;
    color: var(--primary-colours-blue);
  }

  .ant-tabs {
    .ant-tabs-nav {
      margin-bottom: 0;

      .ant-tabs-nav-wrap {
        .ant-tabs-nav-list {
          background-color: var(--lighttheme-content-background-stroke);
          border-radius: 8px;
          padding: 4px;

          .ant-tabs-tab {
            padding: 8px 16px;
            margin: 0 4px;
            border-radius: 6px;
            border: none;
            background: transparent;
            transition: all 0.3s ease;

            .ant-tabs-tab-btn {
              font-size: 14px !important;
              font-weight: 600 !important;
              line-height: 20px !important;
              display: flex;
              align-items: center;
              gap: 8px;
              color: var(--typo-colours-secondary-grey);
              transition: all 0.3s ease;
            }

            &:hover {
              background-color: rgba(0, 0, 0, 0.04);
            }
          }

          .ant-tabs-tab-active {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            .ant-tabs-tab-btn {
              color: var(--primary-colours-blue) !important;
            }
          }
        }
      }
    }

    .ant-tabs-content-holder {
      .ant-tabs-content {
        .ant-tabs-tabpane {
          padding-top: 16px;
        }
      }
    }
  }

  // Card styles for Cost By Tool and Cost By Instructions
  .cost-by-tool-table-card,
  .cost-by-instructions-table-card {
    border-radius: 8px;
    box-shadow: var(--shadow-level-1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: var(--shadow-level-2);
    }

    .ant-card-body {
      padding: 24px;
    }
  }

  // Table styles for Cost By Tool
  .cost-by-tool-table,
  .cost-by-instructions-table {
    width: 100%;

    .ant-table-container {
      overflow-x: auto;
    }

    .ant-table-thead > tr > th {
      background-color: var(--background-light-background-1);
      font-weight: 600;
      color: var(--typo-colours-primary-black);
      border: none;
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      vertical-align: top;
      white-space: normal;
      word-break: break-word;
      border: none;
    }

    .cost-by-tool-table-row,
    .cost-by-instructions-table-row {
      &:hover {
        background-color: var(--background-light-background-2);
      }
    }

    .cost-value {
      font-weight: 500;
      color: var(--primary-colours-blue);
    }
  }

  // Student Tool Cost styles
  .student-tool-cost-container {
    width: 100%;
    font-family: Segoe UI;

    .student-tool-cost-summary-card {
      border-radius: 12px;
      box-shadow: var(--shadow-level-1);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        box-shadow: var(--shadow-level-2);
      }

      .ant-card-body {
        padding: 24px;
      }
    }

    .student-tool-cost-title {
      font-size: 20px;
      font-weight: 700;
      color: var(--typo-colours-primary-black);
      margin-bottom: 24px;
      position: relative;
      padding-left: 16px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 4px;
        background-color: var(--primary-colours-blue);
        border-radius: 4px;
      }
    }

    .student-tool-cost-summary-row {
      margin-bottom: 40px;
    }

    // Stat cards styling
    .student-tool-stat-card {
      height: 100%;
      border-radius: 12px;
      box-shadow: var(--shadow-level-1);
      transition: all 0.3s ease;
      padding: 8px;
      position: relative;
      overflow: hidden;

      &:hover {
        box-shadow: var(--shadow-level-2);
        transform: translateY(-4px);
      }

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
      }

      &.tools-card:before {
        background-color: #52c41a; // Green
      }

      &.submits-card:before {
        background-color: #1890ff; // Blue
      }

      &.cost-card:before {
        background-color: #f5222d; // Red
      }

      .ant-statistic-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        margin-bottom: 12px;
        font-weight: 600;
        color: var(--typo-colours-primary-black);
      }

      .ant-statistic-content {
        font-size: 28px;
        font-weight: 700;
        color: var(--typo-colours-primary-black);
      }

      .stat-description {
        margin-top: 12px;
        font-size: 14px;
        color: var(--typo-colours-secondary-grey);
      }

      .anticon {
        font-size: 20px;
      }

      &.tools-card .anticon {
        color: #52c41a;
      }

      &.submits-card .anticon {
        color: #1890ff;
      }

      &.cost-card .anticon {
        color: #f5222d;
      }
    }

    // Tool detail cards styling
    .student-tool-cost-details {
      .tool-cost-tabs {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: var(--shadow-level-1);
        overflow: hidden;

        .tab-header {
          display: flex;
          border-bottom: 1px solid var(--background-light-background-1);
          padding: 0 16px;

          .tab {
            padding: 16px 24px;
            font-weight: 600;
            color: var(--typo-colours-secondary-grey);
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;

            &.active {
              color: var(--primary-colours-blue);

              &:after {
                content: '';
                position: absolute;
                bottom: -1px;
                left: 0;
                width: 100%;
                height: 2px;
                background-color: var(--primary-colours-blue);
              }
            }
          }
        }

        .tab-content {
          padding: 16px;
        }

        .tools-list {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .tool-list-item {
          border-radius: 8px;
          transition: all 0.3s ease;
          padding: 0;
          overflow: hidden;

          &:hover {
            box-shadow: var(--shadow-level-2);
            transform: translateY(-2px);
          }

          .ant-card-body {
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 16px;
          }

          .tool-rank {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--background-light-background-1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
            color: var(--typo-colours-primary-black);
            flex-shrink: 0;
          }

          .tool-info {
            flex: 1;
            min-width: 0; // Needed for text-overflow to work
          }

          .tool-name-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
          }

          .tool-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--typo-colours-primary-black);
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 70%;
          }

          .tool-percent {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-colours-blue);
            background-color: rgba(24, 144, 255, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
          }

          .tool-stats-row {
            display: flex;
            gap: 24px;
            margin-bottom: 12px;
          }

          .tool-stat {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--typo-colours-secondary-grey);

            .stat-icon {
              font-size: 16px;

              &.submits-icon {
                color: #1890ff;
              }

              &.cost-icon {
                color: #f5222d;
              }
            }

            .cost-value {
              color: #f5222d;
              font-weight: 600;
            }
          }

          .tool-progress {
            position: relative;
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            background-color: var(--background-light-background-1);

            .progress-bar {
              position: absolute;
              top: 0;
              left: 0;
              height: 100%;
              background-color: var(--primary-colours-blue);
              border-radius: 3px;
              z-index: 1;
            }

            .progress-bg {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background-color: var(--background-light-background-1);
              z-index: 0;
            }
          }
        }

        .more-tools-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-top: 16px;
          padding: 12px;
          background-color: var(--background-light-background-1);
          border-radius: 8px;
          color: var(--typo-colours-secondary-grey);
          font-size: 14px;

          .anticon {
            color: var(--primary-colours-blue);
          }
        }
      }
    }
  }

  // Legacy table styles for other tabs
  .open-ai__table {
    width: 100%;

    .ant-table {
      border-radius: 8px;
    }

    th {
      &::before {
        display: none;
      }

      padding: 18px 24px !important;
      margin: -18px -24px;
    }

    .ant-table-cell {
      padding: 18px 24px !important;
      border: 0;
    }

    .ant-table-thead {
      font-family: Segoe UI;
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
    }

    .ant-table-tbody {
      >:last-child {
        :first-child:not(button) {
          border-bottom-left-radius: 8px;
        }

        :last-child:not(button) {
          border-bottom-right-radius: 8px;
        }
      }
    }

    .ant-table-row {
      >.ant-table-cell {
        font-family: Segoe UI;
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
      }

      &:after {
        content: "";
        height: 1px;
        position: absolute;
        left: 24px;
        right: 24px;
        background: var(--table-cell-border-bottom-color);
      }
    }
  }
}