import React, { useState } from "react";
import { Col, Form, Input, Row } from "antd";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import CustomModal from "@component/CustomModal";
import { AntForm } from "@src/app/component/AntForm";

import { createOrganization } from "@services/Organization";
import { toast } from "@src/app/component/ToastProvider";

import RULE from "@rule";

import "./OrganizationCreate.scss";

import * as workspaceRedux from "@src/ducks/workspace.duck";
import * as authRedux from "@src/ducks/auth.duck";

const OrganizationCreate = ({ user, ...props }) => {
  const { t } = useTranslation();
  const { isShowModal, onClose, reloadData } = props;
  const [form] = Form.useForm();

  const [isLoading, setLoading] = useState(false);

  const onFinish = async (values) => {
    setLoading(true);
    const apiResponse = await createOrganization(values);
    if (apiResponse) {
      toast.success("CREATE_ORGANIZATION_SUCCESS");
      if(user?.email === values.email) {
        props.setUser({ ...user, role: "admin", organizationId: apiResponse });
        props.getAvailableWorkspaces();
      }
      reloadData();
      form.resetFields();
      onClose();
    }
    setLoading(false);
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <CustomModal
      isShowModal={isShowModal}
      closeIcon
      handleCancel={handleCancel}
      form="organization-form"
      footerAlign="center"
      width={800}
      okText={t("CREATE")}
      title={t('CREATE_ORGANIZATION')}
      loadingOkButton={isLoading}

    >
      <div >
        <div >
          <AntForm id="organization-form" onFinish={onFinish} layout="vertical" size={"large"} form={form}>
            <Row gutter={24}>
              <Col xs={24} lg={24}>
                <AntForm.Item label={t("ORGANIZATION_NAME")} name="name"
                  rules={[RULE.REQUIRED]}>
                  <Input placeholder={t("ORGANIZATION_NAME_PLACEHOLDER")} />
                </AntForm.Item>
              </Col>
              <Col xs={24} lg={24}>
                <AntForm.Item label="Email" name="email"
                  rules={[{ required: true, lang: "CAN_NOT_BE_BLANK" }, RULE.EMAIL]}>
                  <Input placeholder={t("ENTER_EMAIL")} />
                </AntForm.Item>
              </Col>
            </Row>
          </AntForm>
        </div>
      </div>
    </CustomModal>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...workspaceRedux.actions, ...authRedux.actions };

export default connect(mapStateToProps, mapDispatchToProps)(OrganizationCreate);