import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { useLocation, useNavigate } from "react-router-dom";
import { connect } from "react-redux";
import {Card, Col, Form, Input, Row, Select, Tag, Tooltip} from "antd";

import Loading from "@src/app/component/Loading";
import AntButton from "@src/app/component/AntButton";
import TableAdmin from "@src/app/component/TableAdmin";
import { AntForm } from "@component/AntForm";
import OrganizationCreate from "./OrganizationCreate";
import { confirm } from "@component/ConfirmProvider";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import LockIcon from "@src/app/component/SvgIcons/LockIcon";
import UnlockIcon from "@src/app/component/SvgIcons/UnlockIcon";

import { toast } from "@src/app/component/ToastProvider";
import { getAllOrganization } from "@services/Organization";
import {
  convertQueryToObject,
  formatTimeDate,
  convertObjectToQuery,
  paginationConfig,
  cloneObj,
  getColumnSortOrder
} from "@common/functionCommons";
import { handlePagingData } from "@src/common/dataConverter";
import { deleteOrganization, updateOrganization } from "@src/app/services/Organization";

import { BUTTON, PAGINATION_INIT } from "@constant";
import { LINK } from "@link";

import "./OrganizationManagement.scss";

import * as workspaceRedux from "@src/ducks/workspace.duck";
import * as authRedux from "@src/ducks/auth.duck";

function OrganizationManagement({ user, ...props }) {

  const { t, i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  const [organizationList, setOrganizationList] = useState(PAGINATION_INIT);
  const [isLoading, setIsLoading] = useState(false);
  const [isShowModalCreate, setShowModalCreate] = useState(false);
  const [formFilter] = Form.useForm();
  const [isSearchable, setIsSearchable] = useState(false);

  // get query object from url
  const queryParams = useMemo(() => convertQueryToObject(location.search), [location.search]);

  useEffect(() => {
    getOrganizationData();
  }, [location.search]);

  useEffect(() => {
    // Set initial form values from query params
    formFilter.setFieldsValue({
      name: queryParams.name || "",
      active: queryParams.active !== undefined ? queryParams.active : "",
    });

    // Check if form has values to enable search button
    const formValues = formFilter.getFieldsValue();
    const hasValues = Object.values(formValues).some(val => val !== undefined && val !== "");
    setIsSearchable(hasValues);
  }, [queryParams]);

  const getOrganizationData = async () => {
    let newQuery = cloneObj(queryParams);
    newQuery.page ||= PAGINATION_INIT.paging.page;
    newQuery.limit ||= PAGINATION_INIT.paging.pageSize;
    if (newQuery.isDeveloper) newQuery.isDeveloper = JSON.parse(newQuery.isDeveloper);
    setIsLoading(true);
    const response = await getAllOrganization(newQuery);
    if (response) setOrganizationList(handlePagingData(response));
    setIsLoading(false);
  };

  //handle change sort table
  const handleChangeTable = async (pagination, filters, sorter, extra) => {
    if (extra.action === "sort") {
      const newQuerParams = cloneObj(queryParams);
      if (sorter.order) {
        newQuerParams.sort = `${sorter.order === "ascend" ? "" : "-"}${sorter?.columnKey}`;
      } else {
        delete newQuerParams.sort;
      }
      navigate(`?${convertObjectToQuery(newQuerParams)}`, { replace: true });
    }
  };

  const onCreateOrg = () => {
    setShowModalCreate(true);
  };

  const handleDelete = async (id, name) => {
    confirm.delete({
      title: t("DELETE_ORGANIZATION"),
      content: t("CONFIRM_DELETE_ORGANIZATION_WITH_NAME", { name }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deleteOrganization(id);
        if (apiResponse) {
          toast.success(t("DELETE_ORGANIZATION_SUCCESS"));
          getOrganizationData();
          if (user?.organizationId?._id === id) {
            props.setUser({ ...user, organizationId: null, role: 'normal' });
            props.getAvailableWorkspaces();
          }
        } else {
          toast.error(t("DELETE_ORGANIZATION_ERROR"));
          setIsLoading(false);
        }
      },
    });
  }

  const onChangeLockOrganization = async (organizationId, isLock) => {
    confirm.delete({
      content: isLock ? t("CONFIRM_LOCK_ORGANIZATION") : t("CONFIRM_UNLOCK_ORGANIZATION"),
      okText: isLock ? t("LOCK") : t("UNLOCK"),
      handleConfirm: async () => {
        const dataRequest = {
          _id: organizationId,
          active: !isLock
        }
        const apiResponse = await updateOrganization(dataRequest);
        if (apiResponse) {
          getOrganizationData();
          if (user?.organizationId?._id === organizationId) {
            props.setUser({ ...user, organizationId: { ...user.organizationId, active: !organizationList?.active } });
            props.getAvailableWorkspaces();
          }
          const message = isLock ? t("LOCK_ORGANIZATION_SUCCESS") : t("UNLOCK_ORGANIZATION_SUCCESS");
          toast.success(message);
        }
      },
    });
  }

  const toggleModalCreate = () => {
    setShowModalCreate(!isShowModalCreate);
  }

  const onFilterSubmit = (values) => {
    const newQuery = cloneObj(queryParams);

    // Update query with form values
    if (values.name) {
      newQuery.name = values.name;
    } else {
      delete newQuery.name;
    }

    if (values.active !== undefined && values.active !== "") {
      newQuery.active = values.active;
    } else {
      delete newQuery.active;
    }

    // Reset to page 1 when filtering
    newQuery.page = 1;

    // Update URL with new query params
    navigate(`?${convertObjectToQuery(newQuery)}`, { replace: true });
  };

  const clearFormFilter = () => {
    formFilter.resetFields();
    setIsSearchable(false);

    const newQuery = cloneObj(queryParams);
    delete newQuery.name;
    delete newQuery.active;

    // Keep pagination settings
    navigate(`?${convertObjectToQuery(newQuery)}`, { replace: true });
  };

  const onFormChange = () => {
    const formValues = formFilter.getFieldsValue();
    const hasValues = Object.values(formValues).some(val => val !== undefined && val !== "");
    setIsSearchable(hasValues);
  };

  //handle get pagination value and handle change pagination
  const pagination = paginationConfig(organizationList.paging, queryParams, i18n.language);

  const columns = [
    {
      title: t("ORGANIZATION_NAME"),
      dataIndex: "name",
      key: "name",
      width: 200,
      sorter: true,
      sortOrder: getColumnSortOrder("name", queryParams),
      render: (text) => <span className="organization-name-value">{text}</span>,
    },
    {
      title: t("CREATED_AT"),
      dataIndex: "createdAt",
      key: "createdAt",
      align: "center",
      render: formatTimeDate,
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("createdAt", queryParams),
    },
    {
      title: t("ADMIN"),
      dataIndex: "email",
      key: "email",
      render: (values) => values.map(item => item.email),
      width: 300,
    },
    {
      title: t("NUMBER_MEMBERS"),
      dataIndex: "totalUser",
      key: "totalUser",
      align: "center",
      width: 150,
      sorter: true,
      sortOrder: getColumnSortOrder("totalUser", queryParams),
    },
    {
      title: t("STATUS"),
      dataIndex: "active",
      key: "active",
      align: "center",
      width: 150,
      render: value => {
        const statusText = value ? t("ORGANIZATION_ACTIVE") : t("LOCKED");
        return <Tag color={value ? "success" : "error"} className={value ? "status-tag-active" : "status-tag-locked"}>
          {statusText}
        </Tag>
      },
      sorter: true,
      sortOrder: getColumnSortOrder("active", queryParams),
    },
    {
      title: t("ACTION"),
      key: "ACTION",
      align: "center",
      width: 150,
      render: (_, record) => {
        const isLocked = !record?.active;
        return (
          <div className="organization-actions">
            <Tooltip title={t("EDIT_ORGANIZATION")}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className="btn-edit-organization"
                icon={<EditOutlined />}
                onClick={() => navigate(LINK.ADMIN.ORGANIZATION_ID.format(record._id))}
              />
            </Tooltip>
            <Tooltip title={isLocked ? t("UNLOCK") : t("LOCK")}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className="btn-lock-organization"
                icon={isLocked ? <UnlockIcon /> : <LockIcon />}
                onClick={() => onChangeLockOrganization(record._id, !isLocked)}
              />
            </Tooltip>
            <Tooltip title={t("DELETE_ORGANIZATION")}>
              <AntButton
                type={BUTTON.GHOST_WHITE}
                size="small"
                className="btn-delete-organization"
                icon={<DeleteIcon />}
                onClick={() => handleDelete(record._id, record.name)}
              />
            </Tooltip>
          </div>
        )
      },
    },
  ];

  // Loading state is handled by the Loading component

  return (
    <Loading active={isLoading} transparent>
      <div className="organization-management-container">
        <Card className="organization-info-card">
          <div className="organization-info-header">
            <div>
              <h1 className="organization-title">{t("ORGANIZATION_MANAGEMENT")}</h1>
              <p className="organization-description">{t("ORGANIZATION_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size={"large"}
              icon={<PlusOutlined />}
              onClick={onCreateOrg}
              className="btn-create-organization"
            >
              {t("CREATE_ORGANIZATION")}
            </AntButton>
          </div>
        </Card>

        <Card className="organization-filter-card">
          <AntForm
            form={formFilter}
            size={"large"}
            className={"form-filter"}
            onFinish={onFilterSubmit}
            onValuesChange={onFormChange}
            layout="horizontal"
          >
            <Row gutter={24} align="middle">
              <Col xs={24} md={8} lg={8}>
                <AntForm.Item name={"name"} className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_ORGANIZATION_NAME_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <AntForm.Item name="active" className="search-form-item">
                  <Select
                    placeholder={t("FILTER_BY_STATUS")}
                    allowClear
                    options={[
                      { value: true, label: t("ORGANIZATION_ACTIVE") },
                      { value: false, label: t("LOCKED") }
                    ]}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={8} className="search-buttons-col">
                <div className={"search-buttons"}>
                  <AntButton type={BUTTON.GHOST_WHITE} onClick={clearFormFilter}>{t("CLEAR")}</AntButton>
                  <AntButton type={BUTTON.DEEP_NAVY} htmlType={"submit"} disabled={!isSearchable}>{t("SEARCH")}</AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="organization-table-card">
          <TableAdmin
            columns={columns}
            dataSource={organizationList.rows}
            showSorterTooltip={false}
            scroll={{ x: 1000 }}
            rowKey="_id"
            pagination={pagination}
            onChange={handleChangeTable}
            className="organization-table"
            rowClassName={() => "organization-table-row"}
            locale={{ emptyText: t("NO_ORGANIZATIONS_FOUND") }}
          />
        </Card>

        <OrganizationCreate
          isShowModal={isShowModalCreate}
          onClose={toggleModalCreate}
          reloadData={getOrganizationData}
        />
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = { ...workspaceRedux.actions, ...authRedux.actions };

export default connect(mapStateToProps, mapDispatchToProps)(OrganizationManagement);
