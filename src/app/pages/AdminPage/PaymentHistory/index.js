import { useState, useEffect } from 'react';
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import dayjs from 'dayjs';

import AdminSubcriptionInfo from '././SubcriptionInfo';
import AdminTransactionDetail from './TransactionDetail';
import AdminTransactionList from './TransactionList';

import { getAllTransactionByUserID } from "@services/Transaction";

import { LANGUAGE, TRANSACTION_STATUS } from "@constant";

import './PaymentHistory.scss';
import { useLocation, useParams } from "react-router-dom";
import { getSubscriptionByID } from "@services/Subscription";

const AdminPaymentHistory = ({ ...props }) => {
  const { t } = useTranslation();
  const {id} = useParams();
  const {state} = useLocation();


  const [transactionData, setTransactionData] = useState([]);
  const [transactionActive, setTransactionActive] = useState();
 

  useEffect(() => {
    getTransactionData();
  }, []);

  const getTransactionData = async () => {
    const dataResponse = await getAllTransactionByUserID(id);
    if (dataResponse) {
      setTransactionData(dataResponse.map(transaction => ({ ...transaction, key: transaction._id })));
      setTransactionActive(dataResponse[0]);
    }
  };
  

  const onChangeTransaction = (transaction) => {
    setTransactionActive(transaction);
  }

  const formatDate = (dateTime) => {
    const formatString = dayjs.locale() === LANGUAGE.EN ? "MMM-DD-YYYY" : "DD-MM-YYYY";
    return dayjs(dateTime).format(formatString);
  }

  const renderTransactionStatus = (status, responseCode) => {
    switch (status) {
      case TRANSACTION_STATUS.DONE: return <span className="transaction-status-done">{t("SUCCESSFUL_TRANSACTION")}</span>
      case TRANSACTION_STATUS.ERROR: if (responseCode === "24") {
        return <span className="transaction-status-cancel">{t("TRANSACTION_CANCELED")}</span>
      } else return <span className="transaction-status-error">{t("TRANSACTION_FAILED")}</span>
      default: return <span className="transaction-status-processing">{t("TRANSACTION_PROCESSING")}</span>
    }
  }

  return <div className="payment-history-container">
    <AdminSubcriptionInfo subscription={state?.subscription} formatDate={formatDate} />
    <div className="transaction-history">
      <AdminTransactionList
        transactionData={transactionData}
        onChangeTransaction={onChangeTransaction}
        formatDate={formatDate}
        transactionActive={transactionActive}
        renderTransactionStatus={renderTransactionStatus}
      />
      {transactionActive && <AdminTransactionDetail transactionActive={transactionActive} renderTransactionStatus={renderTransactionStatus} />}
    </div>
  </div>
}

export default AdminPaymentHistory;