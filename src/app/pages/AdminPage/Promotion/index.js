import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { connect } from "react-redux";
import { Card, Col, Form, Input, Row, Select, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { CheckOutlined, EditOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";

import Loading from "@src/app/component/Loading";
import TableAdmin from "@src/app/component/TableAdmin";
import AntButton from "@src/app/component/AntButton";
import DeleteIcon from "@component/SvgIcons/DeleteIcon";

import { toast } from "@src/app/component/ToastProvider";
import { paginationConfig, handleSearchParams, formatDate, handleReplaceUrlSearch } from "@src/common/functionCommons";

import { BUTTON, PAGINATION_INIT, PROMOTION_TYPE } from "@constant";
import { handlePagingData } from "@src/common/dataConverter";

import { getAllPromotion, createPromotion, updatePromotion, deletePromotion } from "@src/app/services/Promotion";
import PromotionDetailModal from "./PromotionDetailModal";
import { AntForm } from "@src/app/component/AntForm";
import { getAllPackage } from "@src/app/services/Package";
import { confirm } from "@component/ConfirmProvider";

import "./Promotion.scss";


function Promotion() {
  const location = useLocation();
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();

  const [promotionData, setPromotionData] = useState(PAGINATION_INIT);
  const [allPackage, setAllPackage] = useState([]);
  const [modalState, setModalState] = useState({
    open: false,
    promotion: null,
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const { query, paging } = handleSearchParams(location.search);
    form.setFieldsValue(query);
    getPromotionData(query, paging);
  }, [location.search]);

  useEffect(() => {
    getPackageData();
  }, []);

  const getPackageData = async () => {
    const response = await getAllPackage();
    if (response) {
      setAllPackage(response);
    }
  }

  const getPromotionData = async (query, paging) => {
    setIsLoading(true);
    const searchFields = ["displayText"];
    const populateOpts = ["packageId"];
    const apiResponse = await getAllPromotion(paging, query, searchFields, populateOpts);
    if (apiResponse) {
      setPromotionData(handlePagingData(apiResponse, query));
    }
    setIsLoading(false);
  };

  const handleDelete = async (id, displayText) => {
    confirm.delete({
      title: t("DELETE_PROMOTION"),
      content: t("DELETE_PROMOTION_CONFIRM", { name: displayText }),
      okText: t("DELETE"),
      cancelText: t("CANCEL"),
      handleConfirm: async () => {
        setIsLoading(true);
        const apiResponse = await deletePromotion(id);
        if (apiResponse) {
          if (promotionData.rows.length === 1 && promotionData.paging.page > 1) {
            const { page, pageSize = 10 } = promotionData.paging;
            handleReplaceUrlSearch(page - 1, pageSize, promotionData.query);
          } else {
            getPromotionData(promotionData.query, promotionData.paging);
          }
          toast.success(t("DELETE_PROMOTION_SUCCESS"));
        } else {
          toast.error(t("DELETE_PROMOTION_ERROR"));
          setIsLoading(false);
        }
      },
    });
  }

  const onToggleModal = (promotion) => {
    setModalState(pre => ({ open: !pre.open, promotion }));
  };

  async function onSave(values) {
    setIsLoading(true);
    const isUpdate = Boolean(modalState.promotion);
    const dataRequest = {
      ...values,
      type: values.type,
      ...(isUpdate && { _id: modalState.promotion._id })
    };
    const dataResponse = isUpdate ? await updatePromotion(dataRequest) : await createPromotion(dataRequest);

    if (dataResponse) {
      toast.success(t(isUpdate ? "UPDATE_PROMOTION_SUCCESS" : "CREATE_PROMOTION_SUCCESS"));
      await getPromotionData(promotionData.query, promotionData.paging);
      onToggleModal();
    } else {
      toast.error(t(isUpdate ? "UPDATE_PROMOTION_ERROR" : "CREATE_PROMOTION_ERROR"));
      setIsLoading(false);
    }
  }

  const columns = [
    {
      title: t("DISPLAY_TEXT"),
      dataIndex: "displayText",
      width: 250,
    },
    {
      title: t("DESCRIPTION"),
      dataIndex: "description",
      width: 250,
    },
    {
      title: t("PROMOTION"),
      dataIndex: "discount",
      width: 150,
    },
    {
      title: t("TYPE"),
      dataIndex: "type",
      width: 150,
      render: value => t(PROMOTION_TYPE[value.toUpperCase()].lang)
    },
    {
      title: t("PACKAGE"),
      dataIndex: ["packageId", "name"],
      width: 200,
    },
    {
      title: t("PERIOD"),
      width: 200,
      render: (_, record) => {
        if (!record?.priceIndex) return '';
        const price = record.packageId?.prices?.[record?.priceIndex - 1];
        return price && `${price?.intervalCount} - ${price?.unitName} - ${price?.unitAmount}`;
      }
    },
    {
      title: t("IS_ACTIVATE"),
      dataIndex: "isActive",
      align: "center",
      width: 150,
      render: (value) => (value && <CheckOutlined />),
    },
    {
      title: t("START_DATE").slice(0, -1),
      dataIndex: "startDate",
      key: "startDate",
      align: "center",
      render: formatDate,
      width: 150,
    },
    {
      title: t("END_DATE").slice(0, -1),
      dataIndex: "endDate",
      key: "startDate",
      align: "center",
      render: formatDate,
      width: 150,
    },
    {
      title: t("ACTION"),
      align: "center",
      width: 120,
      render: (_, record) => (
        <div className="promotion-actions">
          <Tooltip title={t("EDIT_PROMOTION")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-edit-promotion"}
              icon={<EditOutlined />}
              onClick={() => onToggleModal(record)}
            />
          </Tooltip>
          <Tooltip title={t("DELETE_PROMOTION")}>
            <AntButton
              type={BUTTON.GHOST_WHITE}
              size="small"
              className={"btn-delete-promotion"}
              icon={<DeleteIcon />}
              onClick={() => handleDelete(record._id, record.displayText)}
            />
          </Tooltip>
        </div>
      ),
    },
  ];

  const pagination = paginationConfig(promotionData.paging, promotionData.query, i18n.language);

  const submitFormFilter = (values) => {
    handleReplaceUrlSearch(1, promotionData.paging.pageSize, values);
  };

  const onClearFilter = () => {
    form.resetFields();
    submitFormFilter({});
  };

  return (
    <Loading active={isLoading} transparent>
      <div className="promotion-container">
        <Card className="promotion-info-card">
          <div className="promotion-info-header">
            <div>
              <h1 className="promotion-title">{t("PROMOTION_MANAGEMENT")}</h1>
              <p className="promotion-description">{t("PROMOTION_MANAGEMENT_DESCRIPTION")}</p>
            </div>
            <AntButton
              type={BUTTON.DEEP_NAVY}
              size={"large"}
              className="btn-create-promotion"
              icon={<PlusOutlined />}
              onClick={() => onToggleModal()}
            >
              {t("CREATE_PROMOTION")}
            </AntButton>
          </div>
        </Card>

        <Card className="promotion-search-card">
          <AntForm form={form} layout="horizontal" size={"large"} className="form-filter" onFinish={submitFormFilter}>
            <Row gutter={24} align="middle">
              <Col xs={24} md={8} lg={6}>
                <AntForm.Item name="displayText" className="search-form-item">
                  <Input
                    placeholder={t("SEARCH_DISPLAY_TEXT_PLACEHOLDER")}
                    allowClear
                    prefix={<SearchOutlined />}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={6}>
                <AntForm.Item name="packageId" className="search-form-item">
                  <Select
                    placeholder={t("SELECT_PACKAGE")}
                    allowClear
                    options={allPackage}
                    fieldNames={{ label: "name", value: "_id" }}
                  />
                </AntForm.Item>
              </Col>
              <Col xs={24} md={8} lg={6}>
                <AntForm.Item name="isActive" className="search-form-item">
                  <Select placeholder={t("IS_ACTIVATE")} allowClear>
                    <Select.Option value="true">{t("YES")}</Select.Option>
                    <Select.Option value="false">{t("NO")}</Select.Option>
                  </Select>
                </AntForm.Item>
              </Col>
              <Col xs={24} md={24} lg={6} className="search-buttons-col">
                <div className="search-buttons">
                  <AntButton size={"large"} type={BUTTON.GHOST_WHITE} onClick={onClearFilter}>
                    {t("CLEAR")}
                  </AntButton>
                  <AntButton size={"large"} htmlType={"submit"} type={BUTTON.DEEP_NAVY}>
                    {t("SEARCH")}
                  </AntButton>
                </div>
              </Col>
            </Row>
          </AntForm>
        </Card>

        <Card className="promotion-table-card">
          <TableAdmin
            columns={columns}
            dataSource={promotionData.rows}
            scroll={{ x: 1000 }}
            pagination={pagination}
            rowKey="_id"
            className="promotion-table"
            rowClassName={() => "promotion-table-row"}
            locale={{ emptyText: t("NO_PROMOTIONS_FOUND") }}
          />
        </Card>

        <PromotionDetailModal {...modalState} allPackage={allPackage} onCancel={onToggleModal} onFinish={onSave} />
      </div>
    </Loading>
  );
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(Promotion);
