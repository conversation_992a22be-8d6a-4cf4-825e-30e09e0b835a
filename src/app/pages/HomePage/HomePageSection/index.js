import clsx from "clsx";
import { useTranslation } from "react-i18next";

import "./HomePage.scss";

function HomePageSection({ ...props }) {
  const { t } = useTranslation();
  
  const { title, icon, info, extra, className } = props;
  
  return <div className={clsx("homepage-section", className)}>
    <div className="homepage-section__header">
      <div className="homepage-section__header-left">
        
        <div className="homepage-section__header-icon">
          <img src={icon} alt="" />
        </div>
        
        <div className="homepage-section__title">
          <div className="homepage-section__title-text">{title}</div>
          &nbsp;<div className="homepage-section__info">{info}</div>
        </div>
      
      </div>
      {!!extra && <div className="homepage-section__header-right">
        {extra}
      </div>}
    </div>
    {props.children}
  </div>;
}

export default HomePageSection;
