
import { useTranslation } from "react-i18next";

import { renderMoney } from "@src/common/functionCommons";

import TICKET_ICON from '@src/asset/icon/ticket/ticket.svg';
import INFO_ICON from '@src/asset/icon/info/info.svg';
import CALENDAR_ICON from '@src/asset/icon/calendar/calendar.svg';

const SubcriptionInfo = ({ ...props }) => {
  const { subscription, formatDate } = props;
  const { t } = useTranslation();

  const renderCost = (packageData, unitPrice) => {
    const price = packageData?.prices.find(item => item?.unitName === unitPrice);
    const cost = renderMoney(price?.unitAmount) + '/ ' + t(unitPrice?.toUpperCase());
    return packageData ? packageData?.name + (subscription?.isFree ? '' : ' - ' + cost) : '';
  }

  return <div className='subcription-info'>
    <div className="subcription-info__title">
      <div className="title__icon">
        <img src={TICKET_ICON} alt="ticket" />
      </div>
      {t('CURRENT_PACKAGE')}
    </div>

    <div className="subcription-info__item">
      <div className="item__label">
        <img src={INFO_ICON} alt="info" />
        {t('PACKAGE_INFO')}
      </div>
      <div className="package__info">{renderCost(subscription?.packageId, subscription?.unitPrice)}</div>
    </div>

    {!subscription?.isFree && <div className="subcription-info__item">
      <div className="item__label">
        <img src={CALENDAR_ICON} alt="calendar" />
        {t('PACKAGE_TERM')}</div>
      <div className="subcription-time">
        <div>
          <span className="subcription-time__label">{t('START_DATE')}</span>
          {formatDate(subscription?.startDate)}
        </div>
        <div>
          <span className="subcription-time__label">{t('END_DATE')}</span>
          {formatDate(subscription?.endDate)}
        </div>
      </div>
    </div>}
  </div>
};

export default SubcriptionInfo;