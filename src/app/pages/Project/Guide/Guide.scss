.guide-container {

  .modal-content {
    background-color: transparent;
    box-shadow: none;
    padding: 0;

    .modal-body {
      display: flex;
      flex-direction: row;
      gap: 24px;

      .guide-content {
        width: calc(100% - 24px - 384px);
        position: relative;
        overflow: hidden;

        .project-content-layout {
          position: relative;
          border-radius: 8px;
          background: var(--background-light-background-2);
          padding: 24px 32px;
          box-shadow: var(--shadow-level-2);
          display: flex;
          flex-direction: column;
          gap: 24px;

          .project-content__body {
            .project-content__submit {
              text-align: center;
            }

            .project-content-output {
              display: flex;
              flex-direction: column;
              gap: 24px;

              .result-response__change-result {
                height: 24px;
                align-self: center;

                .result-response__change-result-content {
                  //margin-top: -4px;
                  display: flex;
                  flex-direction: row;
                  gap: 20px;

                  .change-result__prev,
                  .change-result__next {
                    .ant-btn-icon {
                      svg {
                        width: 24px;
                        height: 24px;

                        path {
                          transition: stroke 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                          //stroke: red
                        }
                      }
                    }
                  }

                  .change-result__next {
                  }

                  .change-result__index {
                    color: var(--primary-colours-blue);
                    font-weight: 600;
                    align-self: center;
                  }
                }

              }
            }
          }
        }

        .content-header {
          display: none;
        }

        .guide-mark {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          z-index: 0;
        }

        .guide-top-mark {
          content: '';
          background: linear-gradient(180deg, rgba(33, 150, 243, 0.8) 0%, rgba(155, 110, 251, 0.8) 100%);
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
        }

        .guide-bottom-mark {
          content: '';
          background: linear-gradient(180deg, rgba(155, 110, 251, 0.8) 0%, rgba(33, 150, 243, 0.8) 100%);
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
        }
      }

      .guide-step {
        width: 384px;
        position: relative;


        .guide-step__border {
          padding: 8px;
          position: absolute;
          border: 2px dashed #FFFFFF;
          display: none;
          width: 364px;

          .guide-step__content {
            padding: 24px;
            background-color: var(--white);
            border-radius: 8px;

            display: flex;
            flex-direction: column;
            gap: 16px;

            .guide-step__header {
              font-size: 24px;
              font-weight: 700;
              color: var(--primary-colours-blue-navy);
              display: flex;
              gap: 8px;
            }

            .guide-step__body {

            }

            .guide-step__footer {
              display: flex;
              flex-direction: row;
              gap: 8px;
              align-self: center;
            }

          }
        }
      }
    }
  }
}