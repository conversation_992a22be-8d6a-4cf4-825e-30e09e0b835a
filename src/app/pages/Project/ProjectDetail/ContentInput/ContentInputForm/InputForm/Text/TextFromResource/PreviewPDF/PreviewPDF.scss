.preview-pdf {

  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  gap: 24px;

  .preview-pdf__remove-file {
    margin-left: auto;
  }

  .preview-pdf__preview {
    width: 100%;
    background: var(--lighttheme-content-background-stroke);


    position: relative;
    /* If you want text inside of it */
    padding-top: 56.25%;

    .preview-pdf__preview-inner {
      position: absolute;
      top: 24px;
      left: 24px;
      bottom: 24px;
      right: 24px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      justify-content: center;
      align-items: center;

      .preview-file__document {
        height: calc(100% - 20px - 16px);
        display: flex;
        justify-content: center;

        .react-pdf__Page {
          height: 100%;

          >* {
            width: 100% !important;
            height: 100% !important;
          }
        }

        .react-pdf__Page__textContent {
          display: none;
        }
      }

      .preview-file__page-select {
        display: flex;
        gap: 16px;
      }
    }
  }

  .preview-pdf__select-page {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;

    #form-select-page {
      display: flex;
      gap: 16px;
      align-items: center;

      .limit-pages-number {
        padding: 9px 48px;
        border-radius: 8px;
        background: var(--lighttheme-background-2);
        border: 1px solid var(--lighttheme-content-background-stroke)
      }

      .ant-form-item {
        margin: 0;

        .ant-form-item-row{
          align-items: center;
        }

        .ant-form-item-label label {
          height: 40px;
          font-weight: 400;
        }

        .ant-input-number {
          width: 107px;
          box-shadow: none;
          border-radius: 8px;

          &:not(:hover) {
            &:not(:focus-within) {
              border: 1px solid var(--lighttheme-content-background-stroke);
            }
          }

          input {
            text-align: center;
            height: 40px;
          }
        }
      }
    }

    .preview-pdf__error {
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--support-colours-yellow-dark);
    }
  }
}