:root {
  --abcd-checkbox: url('../../../../../../../asset/icon/button/radio-light.svg');
  --abcd-checkbox-selected: url('../../../../../../../asset/icon/button/radio-selected-light.svg');
}

[data-theme='dark'] {
  --abcd-checkbox: url('../../../../../../../asset/icon/button/radio-light.svg');
  --abcd-checkbox-selected: url('../../../../../../../asset/icon/button/radio-selected-light.svg');
}

.abcd-output {
  display: flex;
  flex-direction: column;
  gap: 24px;






  .abcd-output__form-container {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .abcd-output__form-item {
      display: flex;
      flex-direction: row;

      .abcd-output__form-action {
        display: flex;
        flex-direction: column;
        width: 56px;
        gap: 24px;
        justify-content: center;
        align-items: center;

        border-radius: 16px 0 0 16px;
        border: 1px solid var(--lighttheme-content-background-stroke);

        .abcd-output__action-up {

        }

        .abcd-output__action-down {

        }
      }

      .abcd-output__form-content {
        margin-left: -1px;
        border-radius: 0 16px 16px 0;
        border: 1px solid var(--lighttheme-content-background-stroke);

        display: flex;
        flex-direction: column;
        padding: 24px;
        gap: 16px;
        width: calc(100% - 55px);


        .abcd-content-question {
          display: flex;
          flex-direction: row;
          gap: 8px;
          height: 40px;

          .abcd-content-question__text {
            display: flex;
            width: calc(100% - 32px);

            .ant-input {
              border-radius: 4px;
              border: 1px solid var(--lighttheme-content-background-stroke);
              font-weight: 700;
              line-height: 20px;
            }
          }

          .abcd-content-question__action {
            display: flex;
            align-items: center;

            .ant-btn {
              box-shadow: var(--shadow-level-2);

              .ant-btn-icon {
                display: flex;
                place-content: center;
              }
            }
          }
        }

        .abcd-content-answer-list {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .abcd-content-answer-item {
            display: flex;
            flex-direction: row;
            gap: 8px;

            .abcd-content-answer__input {
              width: calc(100% - 32px);


              .ant-input-affix-wrapper {
                border-radius: 4px;
                border: 1px solid var(--lighttheme-content-background-stroke);
                padding-left: 10px;
                padding-right: 10px;
              }

              .ant-input-prefix {
                margin-inline-end: 5.5px;
                cursor: pointer;

                .abcd-content-item__radio {
                  height: 20px;
                  background-image: var(--abcd-checkbox);
                  background-repeat: no-repeat;
                  padding-left: 24px;
                  background-position: left;
                }
              }

              .ant-input {
                line-height: 20px; /* 125% */
              }
            }

            .abcd-content-answer__action {
              display: flex;
              align-items: center;

              .ant-btn {
                box-shadow: var(--shadow-level-2);

                .ant-btn-icon {
                  display: flex;
                  place-content: center;
                }
              }
            }

            &.abcd-content-correct-answer {
              //background: red;

              .ant-input-affix-wrapper {
                border-color: var(--primary-colours-blue);
                background: var(--primary-colours-blue-light-2);

                .ant-input-prefix .abcd-content-item__radio {
                  color: var(--primary-colours-blue);
                  background-image: var(--abcd-checkbox-selected);
                }

                .ant-input {
                  background: var(--primary-colours-blue-light-2);
                  color: var(--primary-colours-blue);
                }
              }
            }
          }
        }

        .abcd-content-add-question {
          display: flex;
          flex-direction: row;
          gap: 8px;

          .abcd-content-add-question__text {
            width: calc(100% - 32px);
            user-select: none;
            cursor: pointer;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid var(--lighttheme-content-background-stroke);

            line-height: 20px; /* 125% */
          }

          .abcd-content-add-question__icon {

            display: flex;
            align-items: center;

            .ant-btn {
              box-shadow: var(--shadow-level-2);

              .ant-btn-icon {
                display: flex;
                place-content: center;

                svg {
                  width: 12px;
                  height: 12px;
                }

                path {
                  stroke: #FFFFFF;
                }
              }
            }
          }
        }
      }
    }
  }
}
