import React from "react";

import "./AbcdQuestion.scss";

function AbcdQuestion({ questions }) {
  return <div className="abcd-output__question-container">
    {questions?.map((question, index) => {
      return <div key={index} className="abcd-output__question-item">
        <div className="abcd-output__question-text">
          <div className="abcd-output__question-index">
            <span className="js-index-question-margin">{index + 1}</span>
          </div>
          {question.question}
        </div>
        <div className="abcd-output__question-option-list">
          {question.options?.filter(option => !!option.optionId && !!option.text)
                   .sort((a, b) => a.optionId.localeCompare(b.optionId))
                   .map((option, optionIndex) => {
                     return <div key={option.text + optionIndex} className="abcd-output__question-option-item">
                       {option.optionId}. {option.text}
                     </div>;
                   })}
        
        </div>
      </div>;
    })}
  </div>;
}

export default AbcdQuestion;