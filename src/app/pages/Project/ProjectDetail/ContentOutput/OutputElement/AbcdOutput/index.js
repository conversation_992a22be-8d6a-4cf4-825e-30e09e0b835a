import React from "react";

import { useContent } from "@app/pages/Project/ProjectDetail/LessonProjectContent/Content";
import AbcdValue from "./AbcdValue";
import AbcdForm from "./AbcdForm";

import "./AbcdOutput.scss";

function AbcdOutput({ ...props }) {
  const outputData = props.responseSelected.output;
  if (!Array.isArray(outputData?.questions) || !Array.isArray(outputData?.correctAnswers)) return null;
  
  if (props.isEditing) return <AbcdForm  {...props} />;
  return <AbcdValue  {...props} />;
}

export default AbcdOutput;