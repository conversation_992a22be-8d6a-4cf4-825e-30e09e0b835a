import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";

import { useProject } from "@app/pages/Project";

import CustomCKEditor from "@component/CKEditor";
import { AntForm } from "@component/AntForm";

import RULE from "@rule";

function HtmlQuestionOutputForm({ responseSelected, isEditing, setEditing, ...props }) {
  
  const { t } = useTranslation();
  
  const { handleSubmitResponse } = useProject();
  
  const { content, outputForm } = props;
  
  useEffect(() => {
    setFormData();
  }, [responseSelected]);
  
  
  useEffect(() => {
    if (!isEditing) {
      setFormData();
    }
  }, [isEditing]);
  
  function setFormData() {
    if (responseSelected?.output) {
      //outputForm.setFieldsValue(responseSelected.output);
      outputForm.setFieldsValue({
        questionsHtml: responseSelected.output.questionsHtml || "",
        answersHtml: responseSelected.output.answersHtml || "",
      });
    } else {
      outputForm.resetFields();
    }
  }
  
  function onFinishResponse(values) {
    const dataRequest = { answersHtml: "", questionsHtml: "" };
    handleSubmitResponse(content._id, { _id: responseSelected._id, output: { ...dataRequest, ...values } })
      .then(() => setEditing(false));
  }
  
  return <AntForm
    layout="vertical"
    id={`form-response-${responseSelected._id}`}
    form={outputForm}
    onFinish={onFinishResponse}
    className="html-question-output-form"
  >
    <AntForm.Item
      label={t("QUESTION")}
      name="questionsHtml"
      rules={[RULE.REQUIRED]}
      //getValueFromEvent={(event, editor) => editor.getData()}
      //valuePropName="data"
    >
      <CustomCKEditor />
    </AntForm.Item>
    
    <AntForm.Item
      label={t("ANSWER")}
      name="answersHtml"
      //getValueFromEvent={(event, editor) => editor.getData()}
      //valuePropName="data"
    >
      <CustomCKEditor />
    </AntForm.Item>
  </AntForm>;
}

export default HtmlQuestionOutputForm;