import React from "react";

import './OpenQuestion.scss'

function OpenQuestion({options}){
  return <div className="open-question__question-container">
    {options?.map((option, index) => {
      return <div key={index} className="open-question__question-item">
        <div className="open-question__question-text">
            <span className="open-question__question-index">
              <span className={"js-index-question-margin"}>{option.optionId}</span>
            </span>
          {option.text}
        </div>
      </div>;
    })}
  </div>
}

export default OpenQuestion