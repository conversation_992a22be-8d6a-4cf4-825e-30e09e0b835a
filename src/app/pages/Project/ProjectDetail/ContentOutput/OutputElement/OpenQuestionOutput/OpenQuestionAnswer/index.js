import React from "react";

import "./OpenQuestionAnswer.scss";

function OpenQuestionAnswer({ correctAnswers }) {
  return <div className="open-question__answer-list">
    {correctAnswers?.map(correctAnswer => {
      return <div key={correctAnswer.optionId} className="open-question__answer-item">
        <span className="open-question__answer-index">
          <span className={"js-index-question-margin"}>
            {correctAnswer.optionId}
          </span>
        </span>
        <div className="open-question__answer-text">
          {correctAnswer.correctAnswer}
        </div>
      </div>;
    })}
  </div>;
}

export default OpenQuestionAnswer;