import React, { useEffect } from "react";
import { Button, Form, Input, Radio } from "antd";

import { useProject } from "@app/pages/Project";

import { CONSTANT } from "@constant";
import RULE from "@rule";
import { cloneObj } from "@common/functionCommons";
import { convertArrayToObject } from "@common/dataConverter";

//import ArrowUp from "@component/SvgIcons/ArrowUp";
//import ArrowDown from "@component/SvgIcons/ArrowDown";
import PlusIcon from "@component/SvgIcons/PlusIcon";
import Trash from "@component/SvgIcons/Trash";
import Question from "@app/pages/Project/ProjectDetail/ContentOutput/OutputElement/Base/Question";


function TrueFalseForm({ responseSelected, isEditing, setEditing, ...props }) {
  const { handleSubmitResponse } = useProject();
  const { content, outputForm } = props;
  
  const form = outputForm;
  
  useEffect(() => {
    if (Array.isArray(responseSelected?.output?.questions)) {
      const correctAnswerObj = convertArrayToObject(responseSelected.output.correctAnswers, "questionId");
      
      const questions = responseSelected?.output.questions.map(question => {
        return { ...question, ...correctAnswerObj[question.questionId] };
      });
      form.setFieldsValue({ questions });
    }
  }, [responseSelected]);
  
  
  function moveQuestion(questionIndex, moveType) {
    const values = form.getFieldsValue();
    const relativeIndex = moveType === CONSTANT.MOVE_UP ? questionIndex - 1 : questionIndex + 1;
    [values.questions[questionIndex], values.questions[relativeIndex]] = [values.questions[relativeIndex], values.questions[questionIndex]];
    form.setFieldsValue(values);
  }
  
  
  function onFinish(values) {
    const formValue = cloneObj(values);
    
    const output = {
      correctAnswers: [],
      questions: [],
    };
    
    
    formValue.questions.forEach((question, index) => {
      output.correctAnswers.push({
        questionId: index + 1,
        correctAnswer: question.correctAnswer,
        answerExplain: question.answerExplain,
      });
      output.questions.push({
        questionId: index + 1,
        question: question.question,
      });
    });
    
    handleSubmitResponse(content._id, { _id: responseSelected._id, output })
      .then(() => setEditing(false));
  }
  
  return <div className="true-false-output">
    
    <Form
      layout="vertical"
      id={`form-response-${responseSelected._id}`}
      form={form}
      autoComplete="off"
      onFinish={onFinish}
      scrollToFirstError
    >
      <Form.List name="questions">
        {(fields, { add, remove }) => {
          return <Question>
            {fields.map((field, index) => {
              return <Question.Item
                key={field.key}
                showMoveUp={index}
                showMoveDown={index !== fields.length - 1}
                onMoveUp={() => moveQuestion(index, CONSTANT.MOVE_UP)}
                onMoveDown={() => moveQuestion(index, CONSTANT.MOVE_DOWN)}
                contentClassName="true-false__content"
              >
                <div className="true-false__input">
                  <Form.Item
                    name={[field.name, "question"]}
                    className="true-false__question"
                    rules={[RULE.REQUIRED]}
                  >
                    <Input.TextArea autoSize={{ minRows: 1 }} />
                  </Form.Item>
                  
                  <Form.Item name={[field.name, "correctAnswer"]} className="true-false__radio">
                    <Radio.Group size="large">
                      <Radio.Button value="True">True</Radio.Button>
                      <Radio.Button value="False">False</Radio.Button>
                      <Radio.Button value="Not stated">Not stated</Radio.Button>
                    </Radio.Group>
                  </Form.Item>
                  
                  <Form.Item name={[field.name, "answerExplain"]} className="true-false__explain">
                    <Input.TextArea placeholder="+ Explanation (optional)" autoSize={{ minRows: 1 }} />
                  </Form.Item>
                </div>
                <div className="true-false__action">
                  <Button size="small" shape="circle" onClick={() => remove(field.name)} icon={<Trash />} />
                </div>
              </Question.Item>;
            })}
            
            <Button
              ghost
              shape="circle"
              className="question__add"
              icon={<PlusIcon />}
              onClick={() => add()}
            />
          </Question>;
        }}
      </Form.List>
      {/*<Button onClick={test}>*/}
      {/*  Test*/}
      {/*</Button>*/}
    </Form>
  
  </div>;
}

export default TrueFalseForm;