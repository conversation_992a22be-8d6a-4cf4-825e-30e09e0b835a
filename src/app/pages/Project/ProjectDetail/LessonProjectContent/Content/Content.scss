.project-content__inner .project-content-layout {
  position: relative;
  border-radius: 8px;
  background: var(--background-light-background-2);
  padding: 24px 32px;
  box-shadow: var(--shadow-level-2);
  display: flex;
  flex-direction: column;
  gap: 24px;

  &.project-content-layout-hidden {
    background-color: #F7F7F7 !important;
    opacity: 0.8;

    .project-content-input,
    .project-content-output {
      background-color: #F7F7F7 !important;
    }

    .project-content-input *,
    .project-content-output * {
      opacity: 0.8;
    }
  }


  .project-content__body {
    display: flex;
    flex-direction: column;
    gap: 24px;

    &.project-content__body-hidden {
      display: none;
    }

    &.loading-component .loading-backdrop .loading-spin {
      top: 100px;
    }

    .project-content__submit {
      display: flex;
      justify-content: center;
    }
  }

  .project-content-output {
    display: flex;
    flex-direction: column;
    gap: 24px;
    border-top-color: transparent;

    .result-response__plaintext {
      //color: #858585;
    }

    .project-content-output__title {
      font-weight: 700;
    }

    .project-content-output__add {
      display: flex;
      align-self: center;

      .ant-btn {
        width: 40px;
        height: 40px;
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.10);

        .ant-btn-icon {
          display: flex;
          place-content: center;

          svg {
            width: 20px;
            height: 20px;

            path {
              stroke: var(--primary-colours-blue);
            }
          }
        }
      }
    }

    .result-response__change-result {
      height: 24px;
      align-self: center;

      .result-response__change-result-content {
        //margin-top: -4px;
        display: flex;
        flex-direction: row;
        gap: 20px;

        .change-result__prev,
        .change-result__next {
          .ant-btn-icon {
            svg {
              width: 24px;
              height: 24px;

              path {
                transition: stroke 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                //stroke: red
              }
            }
          }
        }

        .change-result__next {
        }

        .change-result__index {
          color: var(--primary-colours-blue);
          font-weight: 600;
          align-self: center;
        }
      }

    }


    .result-response__output-status {
      width: 496px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin: auto;
      align-items: center;

      .output-status__title {
        line-height: 24px;
        display: flex;
        flex-direction: row;
        gap: 8px;
        justify-content: center;

        font-size: 24px;
        font-weight: 600;

        svg {
          height: 24px;
          width: 24px;
        }
      }

      .output-error__text {
        font-size: 16px;
        line-height: 20px;
        align-self: center;
      }
    }

    flex-wrap: wrap;
  }
}