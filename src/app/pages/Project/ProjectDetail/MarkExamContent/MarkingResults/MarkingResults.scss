.marking-results-container {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .title-input {
    .title-input__content-index {
      display: none;
    }
  }

  .marking-results__change-result {
    display: grid;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 16px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
    }

    .marking-results__change-student {
      .ant-select {
        width: 320px;
      }
    }

    .marking-results__change-index {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }


}