import React, { useEffect, useState } from "react";
import { Document, Page, pdfjs } from "react-pdf";
import { useTranslation } from "react-i18next";

import Loading from "@src/app/component/Loading";
import ModalPreviewPdf from "../../../../MarkExamContent/ExamSubmission/PreviewSubmission/PreviewPdf/ModalPreviewPdf";

import Maximize from "@src/asset/icon/maximize-24.svg";

import "./PreviewPdf.scss";
import { FullscreenOutlined } from "@ant-design/icons";
import clsx from "clsx";

pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url,
).toString();

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

function PreviewPdf({ file, startPage, endPage }) {

  const { t } = useTranslation();

  const [isLoading, setLoading] = useState(false);
  const [openPreview, setOpenPreview] = useState(false);
  const [totalPages, setTotalPages] = useState(0);

  useEffect(() => {
    if (file) {
      setLoading(true);
    }
  }, [file]);

  function onLoadSuccess({ numPages }) {
    setLoading(false);
    setTotalPages(numPages);
  }

  const renderSelectedPages = () => {
    if ((!startPage || !endPage) && totalPages) return t("SELECTED_PAGES").format(1, totalPages);;
    if (endPage !== startPage) return t("SELECTED_PAGES").format(startPage, endPage);
    return t("SELECTED_PAGE").format(startPage);
  }

  const onPreviewPdf = () => {
    setOpenPreview(pre => !pre);
  }

  const showPreviewBackdrop = file && !isLoading;

  return <Loading active={isLoading}
    fixedMiddleScreen
    className={clsx("preview-pdf-container", { "preview-pdf-container__loading": isLoading })}
  >
    <div className="preview-pdf-content">
      <Document
        file={file}
        onLoadSuccess={onLoadSuccess}
        onLoadError={() => setLoading(false)}
        loading=""
      >
        <Page pageNumber={1} loading={null} />
      </Document>
      {!isLoading && <div className="preview-pdf__select-page">{renderSelectedPages()} </div>}
    </div>

    {showPreviewBackdrop && <div className="preview-pdf-backdrop" onClick={onPreviewPdf} >
      <FullscreenOutlined className="preview-icon" />
    </div>}

    <ModalPreviewPdf openPreview={openPreview} onPreviewPdf={onPreviewPdf} file={file} />
  </Loading>;
}

export default PreviewPdf;