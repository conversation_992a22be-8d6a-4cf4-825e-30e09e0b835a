import React, { useState } from "react";
import { Input } from "antd";
import { useTranslation } from "react-i18next";

import { useMarkStudent } from "@app/pages/Project/ProjectDetail/MarkStudentContent";

import AntButton from "@component/AntButton";
import PreviewSubmission from "./PreviewSubmission";

import { BUTTON, CONSTANT } from "@constant";
import { stringSplit } from "@common/functionCommons";

import Trash2 from "@component/SvgIcons/Trash/Trash2";
import Plus from "@component/SvgIcons/Plus";

import "./AssignmentInput.scss";
import ModalFileAssignment from "./ModalFileAssignment";
import { AntForm } from "@src/app/component/AntForm";


function AssignmentInput({ instructionId, toolInfo }) {
  const { t } = useTranslation();
  const { formEssay } = useMarkStudent();
  const { fileData, setFileData } = useMarkStudent();

  const [isShowModal, setShowModal] = useState(false);

  function handleToggleModal() {
    setShowModal(pre => !pre);
  }

  function hanldeRemoveFile() {
    setFileData({});
  }

  return <>
    <div className="exam-submission-container">
      <div className="exam-sub__header">
        <span className="exam-sub__title">
          <span className="exam-sub__primary-title">
            {t("ESSAY")}
          </span>
        </span>

        <div className="exam-sub__add">
          {!fileData?.fileId
            ? <AntButton
              size="small"
              type={BUTTON.DEEP_GREEN}
              icon={<Plus />}
              iconLocation={CONSTANT.RIGHT}
              onClick={() => handleToggleModal()}
            >
              {t("UPLOAD_FILE")}
            </AntButton>
            : <AntButton
              size="small"
              type={BUTTON.DEEP_YELLOW}
              icon={<Trash2 />}
              iconLocation={CONSTANT.RIGHT}
              onClick={() => hanldeRemoveFile()}
            >
              {t("REMOVE_FILE")}
            </AntButton>
          }
        </div>
      </div>

      {!!fileData?.fileId
        ? <PreviewSubmission
          fileId={fileData?.fileId}
          pageRange={{ startPage: fileData?.startPage, endPage: fileData?.endPage }}
        />
        : (<AntForm form={formEssay}>
          <AntForm.Item
            name='text'
            rules={[
              { required: true, message: t("CAN_NOT_BE_BLANK") },
            ]}
          >
            <Input.TextArea
              count={{
                show: true, max: 3000,
                strategy: (txt) => stringSplit(txt).length,
              }}
              autoSize={{ minRows: 3 }}
              placeholder={t('ENTER_ESSAY')}
            />
          </AntForm.Item>
        </AntForm>)}
    </div >
    <ModalFileAssignment
      open={isShowModal}
      onClose={handleToggleModal}
      toolInfo={toolInfo}
      instructionId={instructionId}
      setFileData={setFileData}
      formEssay={formEssay}
    />
  </>;
}

export default AssignmentInput;