import { convertCamelAndNumberToTitleCase } from "./functionCommon";
import { useTranslation } from "react-i18next";
import HtmlContent from "@component/HtmlContent";
import React from "react";

const EssayAssessment = ({ essayAssessment = {} }) => {
  const { t } = useTranslation();
  
  if (!Object.keys(essayAssessment)?.length) return null;
  return <div className="evaluation__content__item evaluation-essay-assessment">
    <div className="content__item__title evaluation-essay-assessment__title">{t("ESSAY_ASSESSMENT")}</div>
    {typeof essayAssessment === "object" ? Object.entries(essayAssessment).map(([key, values], index) => {
      return (
        <div key={index}>
          <div className="essay-assessment__item-title">{convertCamelAndNumberToTitleCase(key)}</div>
          <ul>
            {values.map((item, itemIndex) => {
              return (<li key={itemIndex}>
                <span className="essay-assessment__item-title">{item.category}: </span>
                {item.feedback}
              </li>);
            })}
          </ul>
        </div>
      );
    }) : <HtmlContent dangerouslySetInnerHTML={{ __html: essayAssessment }}/>}
  
  </div>;
};

export default EssayAssessment;