import { useTranslation } from 'react-i18next';

const ImprovedEssay = ({ improvedEssay }) => {
  const { t } = useTranslation();

  if(!improvedEssay) return null;
  return <div className="evaluation__content__item evaluation-improved-essay">
    <div className="content__item__title evaluation-improved-essay__title">{t("IMPROVED_ESSAY")}</div>
    <p>{improvedEssay}</p>
  </div>
}

export default ImprovedEssay;