import React, { useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Collapse, List } from "antd";
import clsx from "clsx";

import { toast } from "@component/ToastProvider";
import { useProject } from "../../..";

import { BUTTON, CONSTANT, PROJECT_TYPE } from "@constant";
import { formatTimeDate } from "@common/functionCommons";
import { updateTemplateNewContent } from "@services/Template";

import AntButton from "@component/AntButton";
import ChevronDown from "@component/SvgIcons/ChevronDown";
import Paper from "@component/SvgIcons/Paper";
import Loading from "@component/Loading";

function UpdateTemplate({ user, templateList, setTemplateList, onCaptureScreen }) {
  const { t } = useTranslation();
  const { projectData } = useProject();
  const projectType = projectData?.type || PROJECT_TYPE.NORMAL;
  
  const [templateSelected, setTemplateSelected] = useState(false);
  const [isExpandTemplateList, setExpandTemplateList] = useState(false);
  const [isLoading, setLoading] = useState(false);
  
  function toggleTemplateList() {
    setExpandTemplateList(prevState => !prevState);
  }
  
  async function handleUpdateTemplate() {
    if (!templateSelected._id) return;
    if (templateSelected.type === CONSTANT.ORGANIZATION &&
      (templateSelected.organizationId._id !== user.organizationId._id || ![CONSTANT.ADMIN, CONSTANT.CONTRIBUTOR].includes(user.role))) {
      return toast.warning({ description: t("YOU_DO_NOT_HAVE_PERMISSION_TO_UPDATE_THIS_TEMPLATE") });
    } else if (templateSelected.type === CONSTANT.SYSTEM && !user.isSystemAdmin) {
      return toast.warning({ description: t("YOU_DO_NOT_HAVE_PERMISSION_TO_UPDATE_THIS_TEMPLATE") });
    } else if (templateSelected.type === CONSTANT.PERSONAL && templateSelected.userId !== user._id) {
      return toast.warning({ description: t("YOU_DO_NOT_HAVE_PERMISSION_TO_UPDATE_THIS_TEMPLATE") });
    }
    
    setLoading(true);
    const apiResponse = await updateTemplateNewContent({ _id: templateSelected._id });
    if (apiResponse) {
      await onCaptureScreen(templateSelected._id);
      setTemplateList(prevState => {
        return prevState.map(template => template._id === apiResponse._id ? apiResponse : template)
                        .sort((a, b) => b.updatedAt.localeCompare(a.updatedAt));
      });
      setTemplateSelected(null);
      toast.success("TEMPLATE_UPDATE_SUCCESS");
    }
    setLoading(false);
  }
  
  if (!templateList?.length) return null;
  const updateBtnText = projectType === PROJECT_TYPE.NORMAL ? t("UPDATE_LESSON_TEMPLATE") : t("UPDATE_EXAM_TEMPLATE");
  
  return <div className="project-activity-item project-activity__update-template">
    <div className="project-activity-label">
      {t("UPDATE_TEMPLATE")}
      
      <AntButton
        className={clsx("btn-toggle-template-list", { "show-list": isExpandTemplateList })}
        icon={<ChevronDown/>}
        size="mini"
        type={BUTTON.WHITE}
        onClick={toggleTemplateList}
      />
    </div>
    
    <Loading active={isLoading}>
      <Collapse
        ghost
        activeKey={isExpandTemplateList ? ["1"] : []}
        className="ant-collapse-no-header"
        items={[{
          key: "1",
          children: <div className="project-template-content">
            <List
              className="project-template-list"
              itemLayout="horizontal"
              dataSource={templateList}
              renderItem={template => (
                <List.Item
                  className={clsx("project-template-item", { "project-template-item__active": !!templateSelected && templateSelected?._id === template?._id })}
                  onClick={() => setTemplateSelected(template)}
                >
                  <List.Item.Meta
                    avatar={<Paper/>}
                    title={template.name}
                    description={`${t("UPDATE_AT")}: ${formatTimeDate(template?.updatedAt)}`}
                  />
                </List.Item>
              )}
            />
            <div className="project-template__action">
              <AntButton
                size="xsmall"
                type={BUTTON.WHITE_BLUE}
                disabled={!templateSelected}
                onClick={handleUpdateTemplate}
              >
                {updateBtnText}
              </AntButton>
            </div>
          </div>,
        }]}
      />
    </Loading>
  
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(UpdateTemplate);