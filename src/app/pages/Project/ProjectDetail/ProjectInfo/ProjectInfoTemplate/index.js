import React, { useEffect, useState } from "react";
import html2canvas from "html2canvas";

import { useProject } from "@app/pages/Project";

import TemplateForm from "./TemplateForm";
import UpdateTemplate from "./UpdateTemplate";
import ScreenshotContent from "@app/pages/Project/ScreenshotContent";

import { getAllTemplate, uploadFileExtractText } from "@services/Template";

import "./ProjectInfoTemplate.scss";

function ProjectInfoTemplate() {
  const { projectId } = useProject();
  
  const [templateList, setTemplateList] = useState(undefined);
  
  useEffect(() => {
    getTemplateList();
  }, [projectId]);
  
  
  async function onCaptureScreen(templateId) {
    const jsProjectContent = document.getElementById("js-screenshot-content");
    const canvas = await html2canvas(jsProjectContent, {
      allowTaint: true,
      useCORS: true,
      windowWidth: 1610,
      //windowHeight: 1080,
    });
    
    if (canvas) {
      await new Promise(resolve => {
        canvas.toBlob(async (imageBlob) => {
          await uploadFileExtractText(templateId, imageBlob);
          resolve();
        });
      });
    }
  }
  
  async function getTemplateList() {
    const apiResponse = await getAllTemplate({ projectId, sort: "-updatedAt" });
    if (Array.isArray(apiResponse)) {
      setTemplateList(apiResponse);
    }
  }
  
  return <>
    <TemplateForm setTemplateList={setTemplateList} onCaptureScreen={onCaptureScreen} />
    <UpdateTemplate templateList={templateList} setTemplateList={setTemplateList} onCaptureScreen={onCaptureScreen} />
    
    <ScreenshotContent />
  </>;
}

export default ProjectInfoTemplate;