import React, { useEffect, useMemo, useState } from "react";
import { Dropdown, Pagination, Popover } from "antd";
import { useTranslation } from "react-i18next";
import _ from "lodash";

import { useResource } from "@app/pages/Resource";

import NoData from "@component/NoData";
import AntButton from "@component/AntButton";
import YoutubeModal from "@component/YoutubeModal";
import PreviewVideo from "@component/PreviewVideo";

import { BUTTON, CONSTANT } from "@constant";

import MoreVertical from "@component/SvgIcons/MoreVertical";
import Trash from "@component/SvgIcons/Trash";
import YOUTUBE_ICON from "@src/asset/resourceIcon/youtube-icon.svg";

import "./ResourceVideo.scss";
import { API } from "@api";
import { formatTimeDate } from "@src/common/functionCommons";

function ResourceVideo() {
  const { t } = useTranslation();
  
  const { resourceData, resourceTypeActive, resourceSearchValue, handleDeleteResource } = useResource();
  
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 8;
  
  const [stateYoutubePreview, setStateYoutubePreview] = useState({
    isShowModal: false,
    linkYoutube: null,
  });
  
  const [stateVideoPreview, setStateVideoPreview] = useState({
    isShowModal: false,
    videoFileId: null,
  });
  
  useEffect(() => {
    if (resourceTypeActive === CONSTANT.VIDEO && currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [resourceSearchValue]);
  
  
  const dataSource = useMemo(() => {
    if (!resourceData?.[CONSTANT.VIDEO]) return [];
    if (!resourceSearchValue) return resourceData[CONSTANT.VIDEO];
    return resourceData[CONSTANT.VIDEO]?.filter(resource => resource.name?.toLowerCase()?.includes(resourceSearchValue?.toLowerCase()));
  }, [resourceData, resourceSearchValue]);

  useEffect(() => {
    if (dataSource.length && (dataSource.length / pageSize) === (currentPage - 1)) {
      setCurrentPage(currentPage - 1);
    }
  }, [dataSource]);
  
  const resourceVideoPagination = useMemo(() => {
    if (!Array.isArray(dataSource)) return [];
    return dataSource.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  }, [dataSource, currentPage, pageSize]);
  
  function handleShowYoutubePreview(isShowModal, linkYoutube = null) {
    if (isShowModal) {
      setStateYoutubePreview({ isShowModal, linkYoutube });
    } else {
      setStateYoutubePreview(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }
  
  function handleShowVideoPreview(isShowModal, videoFileId = null) {
    if (isShowModal) {
      setStateVideoPreview({ isShowModal, videoFileId });
    } else {
      setStateVideoPreview({ isShowModal: false, videoFileId: null });
    }
  }
  
  function onPreview(resource) {
    if (!resource) return;
    const { videoId, offlineVideoId } = resource;
    if (videoId) {
      handleShowYoutubePreview(true, videoId.url);
    } else if (offlineVideoId) {
      handleShowVideoPreview(true, offlineVideoId?.videoFileId?._id);
    }
  }
  
  if (resourceTypeActive !== CONSTANT.VIDEO) return null;
  if (!dataSource?.length) return <NoData />;
  return <>
    <div className="resource-video-container">
      {resourceVideoPagination?.map((item, index) => {
        
        let imgSrc;
        if (item.videoId) {
          imgSrc = _.last(item.videoId.thumbnails)?.url;
        } else if (item.offlineVideoId) {
          imgSrc = API.STREAM_ID.format(item.offlineVideoId.thumbnailFileId);
        }
        
        return <div key={item._id} className="resource-video-item">
          <div
            className="resource-video-embed"
            onClick={() => onPreview(item)}
            //onClick={() => handleShowYoutubePreview(true, item?.videoId?.url)}
          >
            <img src={imgSrc} alt="" />
          </div>
          <div className="resource-video__footer">
            <div className="resource-video__info">
              <div className="resource-video__title line-clamp-2">
                {item?.videoId && <img src={YOUTUBE_ICON} alt="" className="resource-video__youtube-icon" />}
                <Popover className="" placement="topLeft" content={item.name} trigger="hover">
                  {item.name}
                </Popover>
              </div>
              <div className="resource-video__update-at">
                {`${t("UPDATE_AT")}: ${formatTimeDate(item?.updatedAt)}`}
              </div>
            </div>
            <div className="resource-video__action">
              <Dropdown
                menu={{
                  items: [{
                    key: "DELETE",
                    label: t("DELETE"),
                    icon: <Trash />,
                    onClick: () => handleDeleteResource(item),
                  }],
                  className: "action-dropdown-menu",
                }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <AntButton
                  size="tiny"
                  type={BUTTON.GHOST_WHITE}
                  icon={<MoreVertical />}
                  onClick={(e) => e.stopPropagation()}
                />
              </Dropdown>
            </div>
          </div>
        </div>;
      })}
    </div>
    
    {dataSource.length > pageSize && <div className="resource-content__pagination">
      <Pagination
        current={currentPage}
        total={dataSource.length}
        pageSize={pageSize}
        onChange={setCurrentPage}
      />
    </div>}
    
    <YoutubeModal
      isOpen={stateYoutubePreview.isShowModal}
      linkYoutube={stateYoutubePreview.linkYoutube}
      handleCancel={() => handleShowYoutubePreview(false)}
    />
    
    <PreviewVideo
      isOpen={stateVideoPreview.isShowModal}
      videoFileId={stateVideoPreview.videoFileId}
      handleCancel={() => handleShowVideoPreview(false)}
    />
  </>;
}

export default ResourceVideo;