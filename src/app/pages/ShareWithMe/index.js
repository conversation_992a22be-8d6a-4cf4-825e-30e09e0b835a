import { useEffect, useMemo, useState } from "react";
import { useTranslation } from 'react-i18next';
import { connect } from 'react-redux';

import { getAllSharedWithMe } from "@services/Share";

import FolderProjectList from "@component/FolderProjectList";
import Loading from "@component/Loading";

import * as workingRedux from '@src/ducks/working.duck';
import './ShareWithMe.scss';
import { usePageViewTracker } from "@src/ga";

function ShareWithMe({ ...props }) {
  usePageViewTracker("ShareWithMe");
  const [sharedWithMeData, setSharedWithMeData] = useState([]);
  const [isLoading, setLoading] = useState(true);

  const { t } = useTranslation();

  useEffect(() => {
    getShareWithMeData();
  }, []);

  const getShareWithMeData = async () => {
    const dataResponse = await getAllSharedWithMe();
    if (dataResponse) {
      setSharedWithMeData(dataResponse.map(item => item?.folderId || item?.projectId));
    }
    setLoading(false);
  }

  const handleAfterRename = (data) => {
    const newData = sharedWithMeData.map((item) => {
      if (item?._id === data?._id) {
        return { ...item, ...data };
      }
      return item;
    })
    setSharedWithMeData(newData);
  }

  const handleAfterDelete = (data) => {
    const newData = sharedWithMeData.filter((item) => item?._id !== data?._id);
    setSharedWithMeData(newData);
  }

  const handleAfterChangeStarred = ({ _id }) => {
    const newData = sharedWithMeData.map((item) => {
      if (item?._id === _id) {
        return { ...item, isSaved: !item?.isSaved };
      }
      return item;
    })
    setSharedWithMeData(newData);
  }

  if (isLoading) {
    return <Loading active transparent />;
  }

  return <FolderProjectList
    dataSource={sharedWithMeData}
    showOwner
    showOwner
    allowActions
    textNodata={t("NO_DATA")}
    handleAfterRename={handleAfterRename}
    handleAfterDelete={handleAfterDelete}
    handleAfterChangeStarred={handleAfterChangeStarred}
  />;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...workingRedux.actions,
};

export default (connect(mapStateToProps, mapDispatchToProps)(ShareWithMe));
