import { Button } from "antd";
import SPEAKING_PAUSE from "@src/asset/icon/pause/speaking-pause.svg";
import SPEAKING_PLAY from "@src/asset/icon/play/speaking-play.svg";
import { renderAudioDuration } from "@common/functionCommons";
import Slider from "rc-slider";
import ReactPlayer from "react-player";
import React, { useCallback, useEffect, useRef, useState } from "react";


import "./SpeakingAudioPlayer.scss";

function SpeakingAudioPlayer({ audioFile }) {
  
  
  const playerRef = useRef(null);
  
  const [isPlaying, setPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);
  
  
  const [audioFilePath, setAudioFilePath] = useState(null);
  
  useEffect(() => {
    clearData();
    
    if (audioFile) {
      setTimeout(() => {
        setAudioFilePath(URL.createObjectURL(audioFile));
      }, 100);
    } else {
      console.log("stopppppppppppppp");
    }
    
    return () => clearData();
  }, [audioFile]);
  
  function clearData() {
    setAudioFilePath(null);
    playerRef.current?.seekTo(0);
    setPlaying(false);
    setCurrentTime(0);
    setAudioDuration(0);
  }
  
  return <>
    <div className="speaking-audio-player">
      <div className="audio-player__play">
        <Button
          //size="small"
          shape="circle"
          className="ant-btn-compact"
          icon={isPlaying ? <img src={SPEAKING_PAUSE} alt="" /> : <img src={SPEAKING_PLAY} alt="" />}
          onClick={() => {
            if (!audioDuration) return;
            setPlaying(prevState => !prevState);
          }}
        />
      </div>
      
      <div className="audio-player__time">
        {renderAudioDuration(currentTime)}
      </div>
      <div className="audio-player__seek-bar">
        <Slider
          min={0}
          max={audioDuration}
          value={currentTime}
          onChange={value => {
            if (!audioDuration) return;
            setCurrentTime(value);
            playerRef.current.seekTo(value);
          }}
        />
      </div>
      <div className="audio-player__time">
        {renderAudioDuration(audioDuration)}
      </div>
    </div>
    
    
    {audioFilePath && <div style={{ display: "none" }}>
      <ReactPlayer
        ref={playerRef}
        url={audioFilePath}
        playing={isPlaying}
        width="0"
        height="0"
        progressInterval={0}
        onDuration={duration => {
          setAudioDuration(Math.round(duration));
          playerRef.current.seekTo(0);
        }}
        onProgress={({ playedSeconds }) => setCurrentTime(Math.floor(playedSeconds))}
        onEnded={() => {
          setCurrentTime(0);
          setPlaying(false);
          playerRef.current.seekTo(0);
        }}
      />
    </div>}
  </>;
}

export default SpeakingAudioPlayer;