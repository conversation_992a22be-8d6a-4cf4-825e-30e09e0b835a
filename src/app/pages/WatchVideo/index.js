import React, { useEffect, useMemo, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import queryString from "query-string";
import ReactPlayer from "react-player";

import { API } from "@api";

import { getOfflineVideoDetail } from "@services/OfflineVideo";

import "./WatchVideo.scss";

function WatchVideo() {
  const { videoId } = useParams();
  const { search } = useLocation();
  
  const videoRef = React.useRef(null);
  
  const [playing, setPlaying] = useState(false);
  const [videoInfo, setVideoInfo] = useState(null);
  
  const { start, end } = useMemo(() => {
    return queryString.parse(search);
  }, [search]);
  
  useEffect(() => {
    getVideoInfo();
  }, [videoId]);
  
  function onProgress(state) {
    const { playedSeconds } = state;
    if (end < Math.floor(playedSeconds)) {
      videoRef.current.seekTo(start);
      setPlaying(false);
    }
  }
  
  function onDuration() {
    if (videoRef.current && start) {
      videoRef.current.seekTo(start);
    }
  }
  
  async function getVideoInfo() {
    const apiResponse = await getOfflineVideoDetail(videoId);
    if (apiResponse) {
      setVideoInfo(apiResponse);
    }
  }
  
  return <>
    <div className="watch-video-container">
      <div className="watch-video__player">
        <ReactPlayer
          ref={videoRef}
          url={API.STREAM_MEDIA.format(videoInfo?.videoFileId)}
          controls
          playing={playing}
          width="100%"
          height="100%"
          progressInterval={0}
          onDuration={onDuration}
          onProgress={onProgress}
          onPlay={() => setPlaying(true)}
          onPause={() => setPlaying(false)}
        />
      </div>
    </div>
  </>;
}

export default WatchVideo;