// @import "src/app/styles/scroll";

.workspace-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 100%;

  .workspace-header {
    display: grid;
    gap: 24px;

    @media screen and (min-width: 1480px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    @media screen and (max-width: 1479.98px) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    .workspace-header__filter {
      grid-column: span 3/ span 3;
      display: grid;
      grid-template-columns: repeat(3, minmax(0, 1fr));
      gap: 24px;
    }

    .workspace-actions {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      gap: 16px;

      @media screen and (min-width: 1480px) {
        grid-column: span 1/ span 1;
      }

      @media screen and (max-width: 1479.98px) {
        grid-column: span 2/ span 2;
      }

      button {
        flex-shrink: 0;
      }

      .create-new-btn {
        display: flex;
        width: fit-content;
      }

      .workspace-actions__info-button {

        &.workspace-actions__info-button-active {
          background-color: var(--navy-dark);
          border-color: var(--navy-dark);
        }
      }
    }
  }

  .workspace-body {
    display: grid;
    gap: 24px;
    max-height: 100%;
    overflow: hidden;

    @media screen and (min-width: 768px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    @media screen and (max-width: 767.98px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .workspace-content {
      max-height: 100%;
      scroll-behavior: smooth;
      overflow: overlay;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        width: 8px;
        // background-color: var(--backgroun-1st);
        border-radius: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.5);
        border-radius: 6px;
      }

      @media screen and (min-width: 768px) {
        grid-column: span 4/ span 4;
      }

      @media screen and (max-width: 767.98px) {
        grid-column: span 3/ span 3;
      }
    }

    &.workspace-body-show-info {
      .workspace-content {
        @media screen and (min-width: 768px) {
          grid-column: span 3/ span 3;
        }

        @media screen and (max-width: 767.98px) {
          grid-column: span 2/ span 2;
        }
      }
    }
  }

  .workspace-info {
    height: fit-content;
  }

  .workspace-table {
    width: 100%;

    .folder-project-name {
      color: black;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: 100%;
    }

    .ant-table {
      border-radius: 8px;
    }

    .ant-table-thead {
      position: relative;
      gap: 4px;


      th::before {
        display: none;
      }

      .ant-table-cell {
        background: unset;
        color: var(--folder-text-color-heavier);
        font-weight: 600;
        font-size: 16px;
        padding: 20px 24px;
      }
    }

    .ant-table-row {
      position: relative;
      cursor: pointer;

      :hover {
        .star-container {
          opacity: 1;
        }
      }

      &:active {
        &:not(:has(.starred-content:active, .ant-dropdown-open, .ant-dropdown-trigger:active)) {
          .ant-table-cell {
            background: var(--primary-colours-blue-navy-light-2);
          }
        }
      }

      &:after {
        content: "";
        height: 1px;
        position: absolute;
        left: 24px;
        right: 24px;
        background: var(--table-cell-border-bottom-color);
      }

      >* {
        border-radius: 0;
      }
    }

    .ant-table-cell {
      border: none;
      color: #828fa2;
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      padding: 12px 24px;
      align-content: center;

      .ant-btn-icon {
        img {
          width: 24px;
          height: 24px;
        }
      }

      .padding-left-cell {
        color: var(--folder-text-color-heavier);
        font-weight: 600;
        font-size: 16px;
      }

      .padding-right-cell {
        display: flex;
        text-align: center;
        align-items: center;
        justify-content: center;
        height: 100%;

        .grid-item__actions{
          display: flex;
        }

        .more-vertical-btn {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        img {
          cursor: pointer;
          filter: var(--folder-icon-filter);
        }
      }

      .custom-cell {
        display: flex;
        gap: 8px;
        align-items: center;
        word-break: break-word;
        height: 100%;
        align-items: center;

        .user-icon {
          display: flex;
          padding: 10px;
          align-items: flex-start;
          gap: 10px;
          border-radius: 40px;
          background: rgba(56, 53, 53, 0.5);
        }

        .custom-cell__editing {
          display: flex;
          justify-content: space-between;
          width: 100%;
          gap: 8px;

          #editing__input {
            color: var(--folder-text-color-heavier);
            font-size: 16px;
            font-weight: 600;
            background-color: unset;
            border: none;
            padding: 0;
            line-height: 20px;
            // margin-left: 8px;
            box-shadow: unset;
          }

          #editing__submit-button {
            background: unset;
            padding: 0;
            width: 100%;
            height: 100%;

            &:disabled {
              img {
                filter: invert(55%) sepia(0%) saturate(19%) hue-rotate(229deg) brightness(96%) contrast(95%);
              }
            }

            img {
              filter: var(--folder-icon-filter);
            }
          }
        }

        .folder-project-icon {
          width: 24px;
          display: flex;
          justify-content: end;
        }
      }
    }
  }
}