.modal-preview-pdf-mask {
  background-color: #000000B2 !important;
}

.modal-preview-pdf-wraper {
  display: flex;

  .ant-modal {
    top: 0;
    margin: 50px auto;
    width: fit-content !important;
    padding-bottom: 0 !important;
    max-height: 100% !important;
    display: flex;
  }

  .modal-preview-pdf-content {
    background-color: unset !important;
    box-shadow: unset !important;
    border: none !important;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 !important;

    .modal-preview-pdf-body {
      height: 100%;
      width: fit-content;

      .preview-pdf-wrapper {
        height: 100%;

        .react-pdf__Document {
          height: calc(100% - 36px);
          aspect-ratio: 210/297;

          .react-pdf__Page {
            height: 100%;

            >* {
              width: 100% !important;
              height: 100% !important;
            }
          }
        }

        .preview-pdf__select-page {
          padding-top: 16px;
          justify-content: center;
          color: white;
          display: flex;
          gap: 16px;

          svg {
            path {
              stroke: var(--white) !important;
            }
          }
        }
      }
    }
  }
}