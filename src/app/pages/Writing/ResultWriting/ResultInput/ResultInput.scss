@import "src/app/styles/scroll";

.result-writing__input {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: calc(100vh - 245px);
  top: 1px;
  padding-right: 4px;
  grid-column: span 1/ span 1;

  @extend .scrollbar;
  @extend .scrollbar-show;

  .writing-input-section {
    padding: 16px;
    border: 1px solid #DBDBDB;
    display: flex;
    flex-direction: column;
    gap: 8px;
    border-radius: 16px;

    .writing-input-section__title {
      font-weight: 500;
      line-height: 24px;
      color: var(--navy);
    }

    .writing-input-section__topic {
      line-height: 24px;
    }

    .writing-input-section__image-topic {
      height: 250px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .ant-image {
        height: 100%;
        width: auto !important;

        .ant-image-img {
          height: 100% !important;
        }

        .ant-image-mask {
          background: #00000066;

          .image-topic__preview__icon {
            svg {
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }

    .writing-input-section__content {
      margin-top: -8px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .ck-content {
        border: none;
        padding: 0;

        // max-height: calc(100vh - 298px);

        p {
          line-height: 24px;
        }
      }

      .ck.ck-editor__editable_inline>*:first-child {
        margin-top: 8px;
      }

      .ck.ck-editor__editable_inline>*:last-child {
        margin-bottom: 0px;
      }

      .ck.ck-editor__top.ck-reset_all {
        display: none;
      }

      .writing-input-section__content__count {
        //styleName: Clickee/label/medium-prominent;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        color: var(--primary-colours-blue-navy);
        padding-top: 8px;
        border-top: 1px solid #DBDBDB;
      }
    }

    .writing-input-section__extra {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .writing-input-section__extra_item {
        display: flex;
        gap: 2px;
        font-size: 12px;
        font-weight: 500;
        line-height: 20px;
        align-items: center;

        .extra_item__label {
          color: var(--primary-colours-blue-navy);

          &::after {
            content: ":";
          }
        }

        .extra_item__category {
          padding: 2px 10px;
          gap: 2px;
          border-radius: 8px;
          background: #E5F6FF;
          color: #006499;
        }
      }
    }
  }


}