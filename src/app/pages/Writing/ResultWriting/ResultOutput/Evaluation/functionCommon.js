export const calculateIeltsAverage = (evaluations) => {
  // <PERSON><PERSON>h điểm trung bình
  const scores = evaluations.map(evaluation => evaluation.score || evaluation.bandScore || 0);
  const sum = scores.reduce((acc, score) => acc + score, 0);
  const average = sum / scores.length;

  // Làm tròn điểm trung bình đến 0.5 gần nhất
  const roundedAverage = Math.round(average * 2) / 2;

  return roundedAverage;
}

export const convertCamelAndNumberToTitleCase = (text) => {
  const result = text.replace(/([A-Z0-9])/g, ' $1');
  const titleCase = result.charAt(0).toUpperCase() + result.slice(1);
  return titleCase;
}

import EVALUATION_1 from "@src/asset/icon/evaluation/evaluation-1.svg";
import EVALUATION_2 from "@src/asset/icon/evaluation/evaluation-2.svg";
import EVALUATION_3 from "@src/asset/icon/evaluation/evaluation-3.svg";
import EVALUATION_4 from "@src/asset/icon/evaluation/evaluation-4.svg";
import EVALUATION_5 from '@src/asset/icon/evaluation/evaluation-5.svg'
import EVALUATION_6 from "@src/asset/icon/evaluation/evaluation-6.svg";
import EVALUATION_7 from "@src/asset/icon/evaluation/evaluation-7.svg";
import EVALUATION_8 from "@src/asset/icon/evaluation/evaluation-8.svg";
import EVALUATION_9 from "@src/asset/icon/evaluation/evaluation-9.svg";
import EVALUATION_IDEA from "@src/asset/icon/evaluation/evaluation-idea.svg";

export const WRITING_EVALUATION_LIST = {
  suggests: {
    title: "UPGRADE_GRAMMAR_AND_VOCABULARY",
    color: "#0C93FF",
    icon: EVALUATION_1,
  },
  criteria: {
    title: "CRITERIA_ASSESSMENT",
    color: "#059355",
    icon: EVALUATION_7,
  },
  evaluations: {
    title: "CRITERIA_ASSESSMENT",
    color: "#0C93FF",
    icon: EVALUATION_5,
  },
  essayAssessment: {
    title: "ESSAY_ASSESSMENT",
    color: "#3A18CE",
    icon: EVALUATION_2,
  },
  argumention: {
    title: "ARGUMENTS",
    color: "#3A18CE",
    icon: EVALUATION_2,
  },
  improvedEssay: {
    title: "IMPROVED_ESSAY",
    color: "#3A18CE",
    icon: EVALUATION_6,
  },
  sampleEssay: {
    title: "SAMPLE_ESSAY",
    color: "#0C93FF",
    icon: EVALUATION_9,
  },
  vocabularies: {
    title: "VOCABULARY",
    color: "#059355",
    icon: EVALUATION_7,
  },
  ideas: {
    title: "IDEA_SUGGESTION",
    color: "#FF6200",
    icon: EVALUATION_IDEA,
  },
  grammarStructureSuggestion: {
    title: "GRAMMAR_STRUCTURE_SUGGESTION",
    color: "#0C93FF",
    icon: EVALUATION_9,
  }
};
