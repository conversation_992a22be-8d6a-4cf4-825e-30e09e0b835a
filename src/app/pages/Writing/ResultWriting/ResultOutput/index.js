import { useTranslation } from "react-i18next";

import { BUTTON, CONSTANT } from "@constant";
import "./ResultOutput.scss";
import AntButton from "@src/app/component/AntButton";
import { WRITING_EVALUATION_LIST } from "./Evaluation/functionCommon";
import React, { useMemo, useState } from "react";
import SuggestEvaluation from "./Evaluation/SuggestEvaluation";
import EssayAssessment from "./Evaluation/EssayAssessment";
import VocabularyEvaluation from "./Evaluation/VocabularyEvaluation";
import ImprovedEssay from "./Evaluation/ImprovedEssay";
import GrammarStructureSuggestion from "./Evaluation/GrammarStructureSuggestion";
import Arguments from "./Evaluation/Arguments";
import CriteriaAssessment from "./Evaluation/CriteriaAssessment";
import IdeasSuggest from "./Evaluation/IdeasSuggest";
import Evaluation from "./Evaluation/Evaluation";
import ArrowDown from "@component/SvgIcons/ArrowDown";
import ResultWritingScore from "@app/pages/Writing/ResultWriting/ResultOutput/ResultWritingScore";
import { createOneMarkTest } from "@src/app/services/Report";
import { cleanFileName, downloadUsingBrowser, getFileExtension } from "@src/common/functionCommons";
import { API } from "@api";
import SampleEssay from "./Evaluation/SampleEssay";
import FeedbackPopopver from "@src/app/component/FeedbackPopover";
import { actions, paramsCreators, TRACKING_ACTIONS } from "@src/ducks/tracking.duck";
import { useDispatch } from "react-redux";

const ResultOutput = ({ projectData, output, ...props }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { essayText, setEssayText } = props;

  const [evaluationSelected, setEvaluationSelected] = useState(CONSTANT.ALL);

  const {
    suggests, vocabularies, evaluations, criteria, ideas, arguments: argumention,
    improvedEssay, essayAssessment, grammarStructureSuggestion, overallBandScore, sampleEssay,
  } = output || {};

  const feedbackData = {
    suggests, vocabularies, evaluations, criteria, ideas, argumention,
    improvedEssay, essayAssessment, grammarStructureSuggestion, sampleEssay
  };

  const listButton = useMemo(() => {
    return Object.entries(WRITING_EVALUATION_LIST).filter(([key, evaluation]) => !!feedbackData[key])
      ?.map(([key, evaluation]) => {
        return <AntButton
          key={key}
          type={BUTTON.LIGHT_NAVY}
          active={evaluationSelected === key}
          onClick={() => {
            setEvaluationSelected(key);
            dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_TAB, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name, evaluation?.title)))
          }}
        >
          {t(evaluation?.title)}
        </AntButton>;
      });
  }, [feedbackData]);

  const onDownload = async () => {
    const apiRequest = {
      projectId: projectData._id,
      responseId: projectData?.content?.[0]?.responses?.[0]._id,
    };

    const filePrevew = await createOneMarkTest(apiRequest);
    if (filePrevew?.fileName) {
      const displayName = cleanFileName(projectData.projectName) + "." + getFileExtension(filePrevew.fileName);
      const workspaceId = projectData?.workspaceId;
      const logParamsString = `&workspaceId=${workspaceId}&projectId=${projectData._id}`;
      downloadUsingBrowser(API.DOWNLOAD_REPORT_FILE.format(filePrevew.fileName, displayName) + logParamsString);
    }
    dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_DOWNLOAD, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name)))
  }

  return (
    <div className="result-writing-output">
      <div className="result-writing-output__header">
        <AntButton
          type={BUTTON.LIGHT_NAVY}
          active={evaluationSelected === CONSTANT.ALL}
          onClick={() => {
            setEvaluationSelected(CONSTANT.ALL);
            dispatch(actions.trackCustomClick(TRACKING_ACTIONS.CLICK_TAB, paramsCreators.viewResult(projectData?.content?.[0]?.toolId?.name, CONSTANT.ALL)))
          }}
        >
          {t("ALL")}
        </AntButton>
        {listButton?.map((button) => button)}
      </div>
      <div className="result-writing-output__body">
        <div className="result-writing-output__body-content">

          <ResultWritingScore overallBandScore={overallBandScore} />

          <div className="evaluation__content">
            <SuggestEvaluation
              evaluationSelected={evaluationSelected}
              suggests={suggests}
              setAssignment={setEssayText}
              assignment={essayText}
            />
            <CriteriaAssessment criteria={criteria} evaluationSelected={evaluationSelected} />
            <Evaluation evaluations={evaluations} evaluationSelected={evaluationSelected} />
            <EssayAssessment essayAssessment={essayAssessment}
              evaluationSelected={evaluationSelected} />
            <Arguments argumention={argumention} evaluationSelected={evaluationSelected} />
            <ImprovedEssay improvedEssay={improvedEssay} evaluationSelected={evaluationSelected} />
            <SampleEssay sampleEssay={sampleEssay} evaluationSelected={evaluationSelected} />
            <VocabularyEvaluation vocabularies={vocabularies} evaluationSelected={evaluationSelected} />
            <IdeasSuggest ideas={ideas} evaluationSelected={evaluationSelected} />
            <GrammarStructureSuggestion grammarStructureSuggestion={grammarStructureSuggestion}
              evaluationSelected={evaluationSelected} />
          </div>

          <div className="result-writing-output__action">
            <AntButton
              size="mini"
              icon={<ArrowDown />}
              type={BUTTON.WHITE_COBALT}
              iconLocation={CONSTANT.RIGHT}
              onClick={onDownload}
            >
              {t("DOWNLOAD")}
            </AntButton>
            <FeedbackPopopver toolId={projectData?.content?.[0]?.responses?.[0]?.toolId} projectId={projectData?._id} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultOutput;